cargo:rustc-check-cfg=cfg(Py_LIMITED_API)
cargo:rustc-check-cfg=cfg(Py_GIL_DISABLED)
cargo:rustc-check-cfg=cfg(PyPy)
cargo:rustc-check-cfg=cfg(GraalPy)
cargo:rustc-check-cfg=cfg(py_sys_config, values("Py_DEBUG", "Py_REF_DEBUG", "Py_TRACE_REFS", "COUNT_ALLOCS"))
cargo:rustc-check-cfg=cfg(pyo3_disable_reference_pool)
cargo:rustc-check-cfg=cfg(pyo3_leak_on_drop_without_reference_pool)
cargo:rustc-check-cfg=cfg(Py_3_7)
cargo:rustc-check-cfg=cfg(Py_3_8)
cargo:rustc-check-cfg=cfg(Py_3_9)
cargo:rustc-check-cfg=cfg(Py_3_10)
cargo:rustc-check-cfg=cfg(Py_3_11)
cargo:rustc-check-cfg=cfg(Py_3_12)
cargo:rustc-check-cfg=cfg(Py_3_13)
cargo:rustc-check-cfg=cfg(Py_3_14)
cargo:rustc-cfg=rustc_has_once_lock
cargo:rustc-check-cfg=cfg(rustc_has_once_lock)
cargo:rustc-cfg=cargo_toml_lints
cargo:rustc-check-cfg=cfg(cargo_toml_lints)
cargo:rustc-cfg=rustc_has_extern_c_unwind
cargo:rustc-check-cfg=cfg(rustc_has_extern_c_unwind)
cargo:rustc-cfg=invalid_from_utf8_lint
cargo:rustc-check-cfg=cfg(invalid_from_utf8_lint)
cargo:rustc-cfg=c_str_lit
cargo:rustc-check-cfg=cfg(c_str_lit)
cargo:rustc-cfg=diagnostic_namespace
cargo:rustc-check-cfg=cfg(diagnostic_namespace)
cargo:rustc-cfg=io_error_more
cargo:rustc-check-cfg=cfg(io_error_more)
cargo:rustc-cfg=fn_ptr_eq
cargo:rustc-check-cfg=cfg(fn_ptr_eq)
