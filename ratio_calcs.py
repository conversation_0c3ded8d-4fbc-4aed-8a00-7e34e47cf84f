import numpy as np
from numba import njit
import pandas as pd

MIN_FLOAT = 1e-15

@njit
def portfolio_variance_numba(weights, cov_matrix):
    return np.dot(weights, np.dot(cov_matrix, weights))

def portfolio_variance(weights, cov_matrix):
    return portfolio_variance_numba(np.array(weights), np.array(cov_matrix))

@njit
def neg_sharpe_ratio_numba(weights, mean_returns, cov_matrix):
    port_return = 0.0
    n = len(weights)
    # Accumulate weighted returns with safeguard.
    for i in range(n):
        w = weights[i]
        mr = mean_returns[i]
        if w != w:  # if NaN
            w = 0.0
        if mr != mr:
            mr = 0.0
        port_return += w * mr

    # Compute variance using our numba function.
    port_var = portfolio_variance_numba(weights, cov_matrix)
    if port_var != port_var:
        port_var = 0.0
    port_vol = np.sqrt(port_var)
    if port_vol != port_vol:
        port_vol = 0.0

    if port_vol > 0.0:
        return -port_return / port_vol
    else:
        return 0.0

def neg_sharpe_ratio(weights, mean_returns, cov_matrix):
    return neg_sharpe_ratio_numba(np.array(weights), np.array(mean_returns), np.array(cov_matrix))

@njit
def downside_std_numba(arr):
    s = 0.0
    count = 0
    for x in arr:
        # If x is NaN, skip it.
        if x != x:
            continue
        if x < 0:
            s += x * x
            count += 1
    if count > 0:
        return np.sqrt(s / count)
    else:
        return 0.0

@njit
def neg_sortino_ratio_numba(weights, mean_returns, cov_matrix, returns_array):
    port_return = 0.0
    n = len(weights)
    for i in range(n):
        port_return += weights[i] * mean_returns[i]
    
    # Clean the returns_array: replace NaNs with 0.
    clean_returns = np.empty_like(returns_array)
    for i in range(returns_array.shape[0]):
        val = returns_array[i]
        if val != val:
            clean_returns[i] = 0.0
        else:
            clean_returns[i] = val

    # Compute downside standard deviation on negative returns.
    dd = downside_std_numba(clean_returns)
    if dd < MIN_FLOAT:
        return np.inf
    return -port_return / dd

def neg_sortino_ratio(weights, mean_returns, cov_matrix, adjusted):
    # Convert candidate_series to numpy: adjusted.dot(weights)
    candidate_series = adjusted.dot(weights).values.astype(np.float64)
    return neg_sortino_ratio_numba(np.array(weights),
                                   np.array(mean_returns),
                                   np.array(cov_matrix),
                                   candidate_series)

@njit
def compute_omega_ratio_numba(arr, threshold):
    gain_sum = 0.0
    loss_sum = 0.0
    for x in arr:
        if x != x:  # skip NaN
            continue
        if x > threshold:
            gain_sum += (x - threshold)
        elif x < threshold:
            loss_sum += (threshold - x)
    if loss_sum < MIN_FLOAT:
        return np.inf
    return gain_sum / loss_sum

def compute_omega_ratio(candidate_series, threshold=0.0):
    # If candidate_series is a pandas Series, extract its values;
    # otherwise assume it's already a numpy array.
    if hasattr(candidate_series, 'values'):
        arr = candidate_series.values.astype(np.float64)
    else:
        arr = np.array(candidate_series, dtype=np.float64)
    return compute_omega_ratio_numba(arr, threshold)

@njit
def neg_calmar_ratio_numba(weights, mean_returns, adjusted):
    # Calculate portfolio return just for consistency.
    port_return = 0.0
    n = len(weights)
    for i in range(n):
        port_return += weights[i] * mean_returns[i]
        
    # Compute candidate series as the dot product of each row of adjusted and weights.
    T = adjusted.shape[0]
    candidate_series = np.empty(T, dtype=np.float64)
    for t in range(T):
        s = 0.0
        for j in range(n):
            temp = adjusted[t, j] * weights[j]
            if temp != temp:  # safeguard multiplication result
                temp = 0.0
            s += temp
        candidate_series[t] = s
        
    # Compute cumulative returns with safeguard.
    cum_returns = np.empty(T, dtype=np.float64)
    if candidate_series[0] != candidate_series[0]:
        cum_returns[0] = 0.0
    else:
        cum_returns[0] = candidate_series[0]
    for t in range(1, T):
        val = candidate_series[t]
        if val != val:
            val = 0.0
        cum_returns[t] = cum_returns[t - 1] + val
        
    # Compute running peak (max cumulative return so far) with safeguards.
    peak = np.empty(T, dtype=np.float64)
    peak[0] = cum_returns[0]
    for t in range(1, T):
        current = cum_returns[t]
        prev_peak = peak[t - 1]
        # If either value is non-finite, use prev_peak.
        if not (current == current and prev_peak == prev_peak):
            peak[t] = prev_peak
        elif current > prev_peak:
            peak[t] = current
        else:
            peak[t] = prev_peak
    
    # Calculate maximum drawdown safely.
    max_drawdown = 0.0
    for t in range(T):
        d = peak[t] - cum_returns[t]
        if d != d:  # if d is NaN, set to 0.
            d = 0.0
        if d > max_drawdown:
            max_drawdown = d
    if max_drawdown > MIN_FLOAT:
        calmar = cum_returns[-1] / max_drawdown
    else:
        calmar = np.inf
    return -calmar

def neg_calmar_ratio(weights, mean_returns, cov_matrix, adjusted):
    """Calculate negative Calmar ratio (return/max drawdown) for minimization"""
    # Convert adjusted to numpy array directly to avoid Series.swapaxes
    if isinstance(adjusted, pd.DataFrame):
        # Extract values directly to numpy array
        adjusted_arr = adjusted.values.astype(np.float64)
    else:
        adjusted_arr = np.array(adjusted, dtype=np.float64)
        
    # Convert all inputs to numpy arrays to ensure consistency
    weights_arr = np.array(weights, dtype=np.float64)
    mean_returns_arr = np.array(mean_returns, dtype=np.float64)
    
    # Call the numba-optimized function with numpy arrays only
    return neg_calmar_ratio_numba(weights_arr, mean_returns_arr, adjusted_arr)

@njit
def compute_calmar_ratio_numba(arr):
    n = arr.shape[0]
    cum_returns = np.empty(n, dtype=np.float64)
    # Initialize cumulative returns with safeguard.
    if arr[0] != arr[0]:
        cum_returns[0] = 0.0
    else:
        cum_returns[0] = arr[0]
    for i in range(1, n):
        val = arr[i]
        if val != val:
            val = 0.0
        cum_returns[i] = cum_returns[i - 1] + val
    peak = np.empty(n, dtype=np.float64)
    peak[0] = cum_returns[0]
    for i in range(1, n):
        if cum_returns[i] != cum_returns[i] or peak[i - 1] != peak[i - 1]:
            peak[i] = peak[i - 1]
        else:
            peak[i] = cum_returns[i] if cum_returns[i] > peak[i - 1] else peak[i - 1]
    max_drawdown = 0.0
    for i in range(n):
        d = peak[i] - cum_returns[i]
        if d != d:
            d = 0.0
        if d > max_drawdown:
            max_drawdown = d
    final_return = cum_returns[-1]
    if max_drawdown > MIN_FLOAT:
        return final_return / max_drawdown
    else:
        return np.inf

def compute_calmar_ratio(candidate_series):
    """Compute Calmar ratio (return/max drawdown) for a portfolio return series"""
    # Convert Series to numpy array directly to avoid swapaxes operation
    if isinstance(candidate_series, pd.Series):
        arr = candidate_series.values.astype(np.float64)
    else:
        arr = np.array(candidate_series, dtype=np.float64)
    return compute_calmar_ratio_numba(arr)

def neg_total_return(weights, mean_returns):
    port_return = np.dot(weights, mean_returns)
    return -port_return

def portfolio_volatility(weights, cov_matrix):
    port_var = np.dot(weights.T, np.dot(cov_matrix, weights))
    port_vol = np.sqrt(port_var)
    return port_vol

def calculate_var_cvar(returns_series, confidence_level=0.95):
    """Calculate VaR and CVaR for a return series"""
    # Handle empty arrays
    if len(returns_series) == 0:
        return 0.0, 0.0
        
    # Clean the input array (remove NaNs)
    clean_returns = np.array([x for x in returns_series if np.isfinite(x)])
    
    # If no valid returns after cleaning, return zeros
    if len(clean_returns) == 0:
        return 0.0, 0.0
    
    # Sort returns from worst to best
    sorted_returns = np.sort(clean_returns)
    
    # Find the index at the specified confidence level
    index = int((1 - confidence_level) * len(sorted_returns))
    if index < 0:
        index = 0
    
    # VaR is the loss at this confidence level (negative of return)
    var = -sorted_returns[index]
    
    # CVaR is the average loss beyond VaR (negative of average returns below threshold)
    tail_returns = sorted_returns[:index+1]  # Include the VaR point
    if len(tail_returns) > 0:
        cvar = -np.mean(tail_returns)
    else:
        cvar = var  # Fallback if we don't have enough data points
    
    return var, cvar

@njit
def calculate_var_cvar_numba(returns_array, confidence_level=0.95):
    """Numba-optimized VaR and CVaR calculation"""
    # Clean the input array (remove NaNs)
    valid_returns = []
    for x in returns_array:
        if x == x:  # Check if not NaN
            valid_returns.append(x)
    
    # If no valid returns, return zeros
    if len(valid_returns) == 0:
        return 0.0, 0.0
    
    # Sort returns from worst to best
    sorted_returns = np.sort(np.array(valid_returns))
    
    # Find the index at the specified confidence level
    index = int((1 - confidence_level) * len(sorted_returns))
    if index < 0:
        index = 0
    
    # VaR is the loss at this confidence level (negative of return)
    var = -sorted_returns[index]
    
    # CVaR is the average loss beyond VaR (including the VaR point)
    tail_index = index + 1 # Include the VaR point
    if tail_index > 0 and tail_index <= len(sorted_returns):
        tail_sum = 0.0
        for i in range(tail_index): # Loop up to and including index
            tail_sum += sorted_returns[i]
        cvar = -tail_sum / tail_index # Divide by the number of points included
    else:
        cvar = var # Fallback if index is 0 or out of bounds
    
    return var, cvar

def neg_cvar_ratio(weights, mean_returns, cov_matrix, adjusted, confidence_level=0.95):
    """Calculate negative CVAR ratio (return/CVaR) for minimization"""
    try:
        port_return = np.dot(weights, mean_returns)
        
        # Generate return series
        candidate_series = adjusted.dot(weights)
        if hasattr(candidate_series, 'values'):
            values_array = candidate_series.values.astype(np.float64)
        else:
            values_array = np.array(candidate_series, dtype=np.float64)
        
        clean_values = values_array[~np.isnan(values_array)]
        
        if len(clean_values) > 0:
            _, cvar = calculate_var_cvar_numba(clean_values, confidence_level)
        else:
            cvar = MIN_FLOAT
            
        if abs(cvar) < MIN_FLOAT:
            return 0.0
            
        return -port_return / cvar
        
    except (ValueError, TypeError) as e:
        print(f"Error in CVaR calculation: {str(e)}")
        return 0.0

@njit
def compute_martin_ratio_numba(portfolio_returns, risk_free_rate=0.0):
    """
    Calculate Martin Ratio (return / ulcer index)
    Also known as the Ulcer Performance Index (UPI)

    Args:
        portfolio_returns: numpy array of portfolio returns
        risk_free_rate: risk-free rate (annualized), default 0

    Returns:
        martin_ratio: Return divided by Ulcer Index
    """
    if len(portfolio_returns) == 0:
        return 0.0

    # Calculate cumulative returns (1 + return series)
    cum_returns = np.ones(len(portfolio_returns))
    if portfolio_returns[0] != portfolio_returns[0]:  # Check for NaN
        cum_returns[0] = 1.0
    else:
        cum_returns[0] = 1.0 + portfolio_returns[0]

    for i in range(1, len(portfolio_returns)):
        val = portfolio_returns[i]
        if val != val:  # Check for NaN
            val = 0.0
        cum_returns[i] = cum_returns[i-1] * (1.0 + val)

    # Calculate running peak (max cumulative value so far)
    peak = np.ones(len(cum_returns))
    peak[0] = cum_returns[0]
    for i in range(1, len(cum_returns)):
        peak[i] = max(peak[i-1], cum_returns[i])

    # Calculate percentage drawdowns and squared drawdowns for Ulcer Index
    squared_dd_sum = 0.0
    count = 0
    for i in range(len(cum_returns)):
        if cum_returns[i] != cum_returns[i] or peak[i] != peak[i]:  # Check for NaN
            continue
        if peak[i] <= MIN_FLOAT:
            continue

        # Percentage drawdown: (current_value - peak) / peak * 100
        percentage_drawdown = ((cum_returns[i] - peak[i]) / peak[i]) * 100.0
        squared_dd_sum += percentage_drawdown * percentage_drawdown
        count += 1

    # Calculate Ulcer Index (RMS of percentage drawdowns)
    ulcer_index = np.sqrt(squared_dd_sum / count) if count > 0 else 0.0

    # Calculate total return
    total_return = cum_returns[-1] - 1.0

    # Annualize the return properly using compound formula
    # Assuming daily data: (1 + total_return)^(252/periods) - 1
    if len(portfolio_returns) > 1:
        annualized_return = (1.0 + total_return) ** (252.0 / len(portfolio_returns)) - 1.0
    else:
        annualized_return = total_return * 252.0  # Fallback for single period

    # Calculate Martin Ratio (UPI)
    if ulcer_index > MIN_FLOAT:
        martin_ratio = (annualized_return - risk_free_rate) / ulcer_index * 100.0  # Multiply by 100 to match percentage scale
    else:
        martin_ratio = 0.0 if annualized_return <= risk_free_rate else np.inf

    return martin_ratio

def compute_martin_ratio(candidate_series, risk_free_rate=0.0):
    """Compute Martin Ratio (Return / Ulcer Index) for a portfolio return series"""
    # Convert Series to numpy array directly
    if isinstance(candidate_series, pd.Series):
        arr = candidate_series.values.astype(np.float64)
    else:
        arr = np.array(candidate_series, dtype=np.float64)
    return compute_martin_ratio_numba(arr, risk_free_rate)

@njit
def neg_martin_ratio(weights, mean_returns, cov_matrix, adjusted, risk_free_rate=0.0):
    """Calculate negative Martin ratio for minimization"""
    # Calculate portfolio return series
    portfolio_returns = np.zeros(adjusted.shape[0])
    
    for i in range(adjusted.shape[0]):
        for j in range(len(weights)):
            portfolio_returns[i] += adjusted[i, j] * weights[j]
    
    # Calculate martin ratio using the numba-optimized function
    martin = compute_martin_ratio_numba(portfolio_returns, risk_free_rate)
    
    # Return negative value for minimization
    return -martin

@njit
def neg_modified_sharpe_ratio_numba(weights, mean_returns, cov_matrix, returns_array, risk_free_rate=0.0):
    n = len(weights)
    # Compute portfolio expected return using mean returns
    port_return = 0.0
    for i in range(n):
        port_return += weights[i] * mean_returns[i]
    
    # Compute portfolio variance
    port_var = 0.0
    for i in range(n):
        for j in range(n):
            port_var += weights[i] * cov_matrix[i, j] * weights[j]
    port_vol = np.sqrt(port_var)
    if port_vol <= MIN_FLOAT:
        return 0.0

    # Compute portfolio return series from returns_array (each row is a return observation)
    T = returns_array.shape[0]
    port_series = np.empty(T, dtype=np.float64)
    for t in range(T):
        s = 0.0
        for j in range(n):
            s += returns_array[t, j] * weights[j]
        port_series[t] = s

    # Calculate sample mean and standard deviation of the series
    T_float = float(T)
    series_mean = 0.0
    for t in range(T):
        series_mean += port_series[t]
    series_mean /= T_float

    series_std = 0.0
    for t in range(T):
        diff = port_series[t] - series_mean
        series_std += diff * diff
    series_std = np.sqrt(series_std / T_float)
    
    # Compute skewness and kurtosis
    skew = 0.0
    kurt = 0.0
    if series_std > MIN_FLOAT:
        for t in range(T):
            diff_norm = (port_series[t] - series_mean) / series_std
            skew += diff_norm ** 3
            kurt += diff_norm ** 4
        skew /= T_float
        kurt /= T_float
    else:
        skew = 0.0
        kurt = 3.0  # Normal distribution assumption

    # Use the 95% confidence level: standard normal quantile.
    z = 1.****************
    # Cornish–Fisher expansion adjustment
    z_mod = z + (1.0/6.0) * (z*z - 1.0) * skew + (1.0/24.0) * (z**3 - 3.0*z) * (kurt - 3.0) - (1.0/36.0) * (2.0*z**3 - 5.0*z) * (skew**2)
    # Adjust volatility: scaling the normal volatility to account for skew/kurtosis
    mod_vol = port_vol * (z_mod / z)
    
    # Modified Sharpe ratio (for minimization, return negative value)
    sharpe_mod = (port_return - risk_free_rate) / mod_vol
    return -sharpe_mod

def neg_modified_sharpe_ratio(weights, mean_returns, cov_matrix, adjusted, risk_free_rate=0.0):
    """
    Wrapper for the Numba-optimized modified Sharpe ratio calculation.
    'adjusted' is expected to be a pandas DataFrame or similar; its values will be used to compute the portfolio return series.
    """
    # Convert to numpy array if needed.
    if hasattr(adjusted, 'values'):
        arr = adjusted.values.astype(np.float64)
    else:
        arr = np.array(adjusted, dtype=np.float64)
    return neg_modified_sharpe_ratio_numba(
        np.array(weights, dtype=np.float64),
        np.array(mean_returns, dtype=np.float64),
        np.array(cov_matrix, dtype=np.float64),
        arr,
        risk_free_rate
    )
