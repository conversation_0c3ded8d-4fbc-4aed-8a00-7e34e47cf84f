# Python cache and compiled files
__pycache__/
*.pyc
*.pyo
*.pyd
*.pdb
*.egg-info/
dist/
build/

# Application cache and temporary files
cache_states/
html_cache/
crawl/
*.html
*.htm
*.log
debug_log.log

# Rust build artifacts and cache
target/
Cargo.lock
.cargo/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Virtual environments
venv/
.venv/
env/
.env/

# Environment and configuration files (if they contain secrets)
.env
*.env.local
*.env.production

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Data files that shouldn't be versioned
*.csv
*.xlsx
*.xls
data/
datasets/

# Jupyter notebook checkpoints
.ipynb_checkpoints/

# pytest cache
.pytest_cache/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# mypy cache
.mypy_cache/
.dmypy.json
dmypy.json