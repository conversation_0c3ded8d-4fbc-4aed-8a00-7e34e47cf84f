#!/usr/bin/env python3
"""
Comprehensive Performance Testing and Validation Suite
for Rust-optimized Efficient Frontier Implementation

Tests performance, accuracy, and scaling characteristics
across realistic MPT workloads with increased parameters.
"""

import sys
import time
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple
import matplotlib.pyplot as plt
import json
from datetime import datetime

# Import the Rust-optimized functions
try:
    from efficient_frontier_rust_wrapper import (
        efficient_frontier_upper_hull, 
        efficient_frontier_upper_hull_cvar, 
        get_rust_status,
        efficient_frontier_upper_hull_python,
        efficient_frontier_upper_hull_cvar_python
    )
    RUST_AVAILABLE = True
except ImportError as e:
    print(f"❌ Failed to import Rust functions: {e}")
    RUST_AVAILABLE = False

class PerformanceValidator:
    """Comprehensive performance testing and validation"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'rust_available': RUST_AVAILABLE,
            'test_results': {},
            'performance_metrics': {},
            'accuracy_validation': {},
            'scaling_analysis': {}
        }
        
        if RUST_AVAILABLE:
            status = get_rust_status()
            self.results['rust_status'] = status
            print(f"🦀 Rust Status: {status}")
        
    def generate_realistic_candidates(self, n: int, scenario: str = "balanced") -> List[Dict[str, Any]]:
        """Generate realistic portfolio candidates based on MPT scenarios"""
        np.random.seed(42)  # Reproducible results
        candidates = []
        
        # Define realistic parameter ranges based on MPT usage
        if scenario == "conservative":
            risk_range = (0.005, 0.025)    # 0.5% to 2.5%
            return_range = (0.0001, 0.001) # 0.01% to 0.1%
        elif scenario == "aggressive":
            risk_range = (0.02, 0.08)      # 2% to 8%
            return_range = (0.001, 0.005)  # 0.1% to 0.5%
        else:  # balanced
            risk_range = (0.01, 0.05)      # 1% to 5%
            return_range = (0.0005, 0.003) # 0.05% to 0.3%
        
        for i in range(n):
            # Generate correlated risk/return (higher risk -> higher potential return)
            risk = np.random.uniform(*risk_range)
            base_return = np.random.uniform(*return_range)
            # Add risk premium correlation
            return_val = base_return + (risk * np.random.uniform(0.1, 0.3))
            
            # CVaR typically 1.2-2.5x higher than standard risk
            cvar_95 = risk * np.random.uniform(1.2, 2.5)
            
            candidate = {
                'risk': risk,
                'return': return_val,
                'cvar_95': cvar_95,
                'combo': f'Portfolio_{scenario}_{i}',
                'weights': np.random.dirichlet(np.ones(np.random.randint(3, 8))).tolist(),
                'optimization': np.random.choice(['MaxSharpe', 'MaxSortino', 'MinVar', 'MaxReturn'])
            }
            candidates.append(candidate)
        
        return candidates
    
    def test_accuracy_validation(self) -> Dict[str, Any]:
        """Validate mathematical accuracy between Rust and Python implementations"""
        print("\n🔬 Testing Mathematical Accuracy")
        print("=" * 50)
        
        accuracy_results = {}
        test_sizes = [100, 500, 1000, 2000]
        
        for size in test_sizes:
            print(f"  Testing {size} candidates...")
            candidates = self.generate_realistic_candidates(size, "balanced")
            
            # Test standard efficient frontier
            if RUST_AVAILABLE:
                rust_hull = efficient_frontier_upper_hull(candidates)
                python_hull = efficient_frontier_upper_hull_python(candidates)
                
                # Compare results
                rust_points = set(rust_hull)
                python_points = set(python_hull)
                
                # Calculate accuracy metrics
                intersection = rust_points.intersection(python_points)
                union = rust_points.union(python_points)
                jaccard_similarity = len(intersection) / len(union) if union else 1.0
                
                accuracy_results[f'standard_{size}'] = {
                    'rust_points': len(rust_hull),
                    'python_points': len(python_hull),
                    'jaccard_similarity': jaccard_similarity,
                    'identical_results': rust_hull == python_hull
                }
                
                # Test CVaR efficient frontier
                rust_cvar_hull = efficient_frontier_upper_hull_cvar(candidates)
                python_cvar_hull = efficient_frontier_upper_hull_cvar_python(candidates)
                
                rust_cvar_points = set(rust_cvar_hull)
                python_cvar_points = set(python_cvar_hull)
                
                cvar_intersection = rust_cvar_points.intersection(python_cvar_points)
                cvar_union = rust_cvar_points.union(python_cvar_points)
                cvar_jaccard = len(cvar_intersection) / len(cvar_union) if cvar_union else 1.0
                
                accuracy_results[f'cvar_{size}'] = {
                    'rust_points': len(rust_cvar_hull),
                    'python_points': len(python_cvar_hull),
                    'jaccard_similarity': cvar_jaccard,
                    'identical_results': rust_cvar_hull == python_cvar_hull
                }
                
                print(f"    Standard: {len(rust_hull)} vs {len(python_hull)} points, "
                      f"Similarity: {jaccard_similarity:.3f}")
                print(f"    CVaR: {len(rust_cvar_hull)} vs {len(python_cvar_hull)} points, "
                      f"Similarity: {cvar_jaccard:.3f}")
        
        return accuracy_results
    
    def test_performance_scaling(self) -> Dict[str, Any]:
        """Test performance scaling across different dataset sizes"""
        print("\n⚡ Testing Performance Scaling")
        print("=" * 50)
        
        # Test sizes matching user's increased parameters
        test_sizes = [500, 1000, 2000, 5000, 10000, 20000]
        scenarios = ["conservative", "balanced", "aggressive"]
        
        scaling_results = {}
        
        for scenario in scenarios:
            print(f"\n  Scenario: {scenario.title()}")
            scenario_results = {}
            
            for size in test_sizes:
                print(f"    Testing {size:,} candidates...", end=" ")
                candidates = self.generate_realistic_candidates(size, scenario)
                
                # Test Rust performance
                if RUST_AVAILABLE:
                    start_time = time.perf_counter()
                    rust_hull = efficient_frontier_upper_hull(candidates)
                    rust_time = time.perf_counter() - start_time
                    
                    start_time = time.perf_counter()
                    rust_cvar_hull = efficient_frontier_upper_hull_cvar(candidates)
                    rust_cvar_time = time.perf_counter() - start_time
                else:
                    rust_time = rust_cvar_time = float('inf')
                    rust_hull = rust_cvar_hull = []
                
                # Test Python performance for comparison
                start_time = time.perf_counter()
                python_hull = efficient_frontier_upper_hull_python(candidates)
                python_time = time.perf_counter() - start_time
                
                start_time = time.perf_counter()
                python_cvar_hull = efficient_frontier_upper_hull_cvar_python(candidates)
                python_cvar_time = time.perf_counter() - start_time
                
                # Calculate speedup
                speedup = python_time / rust_time if rust_time > 0 else 0
                cvar_speedup = python_cvar_time / rust_cvar_time if rust_cvar_time > 0 else 0
                
                scenario_results[size] = {
                    'rust_time': rust_time,
                    'python_time': python_time,
                    'rust_cvar_time': rust_cvar_time,
                    'python_cvar_time': python_cvar_time,
                    'speedup': speedup,
                    'cvar_speedup': cvar_speedup,
                    'rust_hull_points': len(rust_hull),
                    'python_hull_points': len(python_hull),
                    'candidates_per_second': size / rust_time if rust_time > 0 else 0
                }
                
                print(f"Rust: {rust_time:.4f}s, Python: {python_time:.4f}s, "
                      f"Speedup: {speedup:.2f}x")
            
            scaling_results[scenario] = scenario_results
        
        return scaling_results
    
    def test_memory_efficiency(self) -> Dict[str, Any]:
        """Test memory usage patterns"""
        print("\n💾 Testing Memory Efficiency")
        print("=" * 50)
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_results = {}
        
        test_sizes = [1000, 5000, 10000, 20000]
        
        for size in test_sizes:
            print(f"  Testing memory with {size:,} candidates...")
            
            # Measure baseline memory
            baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            candidates = self.generate_realistic_candidates(size, "balanced")
            after_generation = process.memory_info().rss / 1024 / 1024
            
            # Test Rust memory usage
            if RUST_AVAILABLE:
                _ = efficient_frontier_upper_hull(candidates)
                after_rust = process.memory_info().rss / 1024 / 1024
            else:
                after_rust = after_generation
            
            # Test Python memory usage
            _ = efficient_frontier_upper_hull_python(candidates)
            after_python = process.memory_info().rss / 1024 / 1024
            
            memory_results[size] = {
                'baseline_mb': baseline_memory,
                'after_generation_mb': after_generation,
                'after_rust_mb': after_rust,
                'after_python_mb': after_python,
                'generation_overhead_mb': after_generation - baseline_memory,
                'rust_overhead_mb': after_rust - after_generation,
                'python_overhead_mb': after_python - after_rust
            }
            
            print(f"    Generation: +{after_generation - baseline_memory:.1f}MB, "
                  f"Rust: +{after_rust - after_generation:.1f}MB, "
                  f"Python: +{after_python - after_rust:.1f}MB")
        
        return memory_results

    def test_real_world_scenarios(self) -> Dict[str, Any]:
        """Test performance under real-world MPT usage scenarios"""
        print("\n🌍 Testing Real-World Scenarios")
        print("=" * 50)

        scenarios = {
            'user_increased_params': {
                'description': 'User\'s increased computational parameters',
                'max_combos': 100000,
                'candidates_per_category': 2000,
                'composite_portfolios': 200,
                'test_size': 12000  # 6 categories * 2000 candidates
            },
            'original_params': {
                'description': 'Original MPT parameters',
                'max_combos': 20000,
                'candidates_per_category': 200,
                'composite_portfolios': 20,
                'test_size': 1200   # 6 categories * 200 candidates
            },
            'extreme_load': {
                'description': 'Extreme computational load test',
                'max_combos': 200000,
                'candidates_per_category': 5000,
                'composite_portfolios': 500,
                'test_size': 30000  # 6 categories * 5000 candidates
            }
        }

        real_world_results = {}

        for scenario_name, config in scenarios.items():
            print(f"\n  {config['description']}")
            print(f"    Testing {config['test_size']:,} candidates...")

            candidates = self.generate_realistic_candidates(config['test_size'], "balanced")

            # Simulate parallel processing groups (as in matrix23.py)
            group_size = config['candidates_per_category']
            groups = [candidates[i:i+group_size] for i in range(0, len(candidates), group_size)]

            # Test sequential processing time
            total_rust_time = 0
            total_python_time = 0
            total_hull_points = 0

            for i, group in enumerate(groups):
                if RUST_AVAILABLE:
                    start_time = time.perf_counter()
                    rust_hull = efficient_frontier_upper_hull(group)
                    rust_time = time.perf_counter() - start_time
                    total_rust_time += rust_time
                    total_hull_points += len(rust_hull)

                start_time = time.perf_counter()
                python_hull = efficient_frontier_upper_hull_python(group)
                python_time = time.perf_counter() - start_time
                total_python_time += python_time

                if i < 3:  # Show progress for first few groups
                    print(f"      Group {i+1}: Rust {rust_time:.4f}s, Python {python_time:.4f}s")

            speedup = total_python_time / total_rust_time if total_rust_time > 0 else 0

            real_world_results[scenario_name] = {
                'config': config,
                'total_rust_time': total_rust_time,
                'total_python_time': total_python_time,
                'speedup': speedup,
                'groups_processed': len(groups),
                'total_hull_points': total_hull_points,
                'avg_time_per_group_rust': total_rust_time / len(groups) if groups else 0,
                'avg_time_per_group_python': total_python_time / len(groups) if groups else 0,
                'throughput_candidates_per_sec': config['test_size'] / total_rust_time if total_rust_time > 0 else 0
            }

            print(f"    Total: Rust {total_rust_time:.3f}s, Python {total_python_time:.3f}s")
            print(f"    Speedup: {speedup:.2f}x, Throughput: {config['test_size'] / total_rust_time if total_rust_time > 0 else 0:,.0f} candidates/sec")

        return real_world_results

    def generate_performance_report(self) -> str:
        """Generate comprehensive performance report"""
        print("\n📊 Generating Performance Report")
        print("=" * 50)

        # Run all tests
        self.results['accuracy_validation'] = self.test_accuracy_validation()
        self.results['scaling_analysis'] = self.test_performance_scaling()
        self.results['memory_efficiency'] = self.test_memory_efficiency()
        self.results['real_world_scenarios'] = self.test_real_world_scenarios()

        # Generate summary
        report = []
        report.append("# Rust Efficient Frontier Performance Validation Report")
        report.append(f"Generated: {self.results['timestamp']}")
        report.append(f"Rust Available: {self.results['rust_available']}")
        report.append("")

        # Accuracy Summary
        report.append("## Mathematical Accuracy Validation")
        if self.results['accuracy_validation']:
            accuracy_data = self.results['accuracy_validation']
            avg_similarity = np.mean([v['jaccard_similarity'] for v in accuracy_data.values()])
            report.append(f"- Average Jaccard Similarity: {avg_similarity:.4f}")
            report.append(f"- Tests Passed: {len(accuracy_data)} scenarios")
            identical_count = sum(1 for v in accuracy_data.values() if v['identical_results'])
            report.append(f"- Identical Results: {identical_count}/{len(accuracy_data)} tests")

        # Performance Summary
        report.append("\n## Performance Scaling Analysis")
        if self.results['scaling_analysis']:
            for scenario, data in self.results['scaling_analysis'].items():
                max_size = max(data.keys())
                max_speedup = data[max_size]['speedup']
                max_throughput = data[max_size]['candidates_per_second']
                report.append(f"- {scenario.title()}: {max_speedup:.2f}x speedup, {max_throughput:,.0f} candidates/sec")

        # Real-world Performance
        report.append("\n## Real-World Scenario Performance")
        if self.results['real_world_scenarios']:
            for scenario, data in self.results['real_world_scenarios'].items():
                report.append(f"- {data['config']['description']}: {data['speedup']:.2f}x speedup")
                report.append(f"  - Processing time: {data['total_rust_time']:.3f}s vs {data['total_python_time']:.3f}s")
                report.append(f"  - Throughput: {data['throughput_candidates_per_sec']:,.0f} candidates/sec")

        # Save detailed results
        with open('performance_validation_results.json', 'w') as f:
            json.dump(self.results, f, indent=2, default=str)

        report_text = "\n".join(report)
        with open('performance_validation_report.md', 'w') as f:
            f.write(report_text)

        return report_text

def main():
    """Run comprehensive performance validation"""
    print("🦀 Rust Efficient Frontier Performance Validation Suite")
    print("=" * 60)

    validator = PerformanceValidator()

    if not RUST_AVAILABLE:
        print("❌ Rust functions not available. Please ensure the module is built and installed.")
        return 1

    try:
        report = validator.generate_performance_report()
        print("\n" + "=" * 60)
        print("📋 PERFORMANCE VALIDATION SUMMARY")
        print("=" * 60)
        print(report)
        print("\n✅ Performance validation completed successfully!")
        print("📁 Detailed results saved to:")
        print("   - performance_validation_results.json")
        print("   - performance_validation_report.md")
        return 0

    except Exception as e:
        print(f"\n❌ Performance validation failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
