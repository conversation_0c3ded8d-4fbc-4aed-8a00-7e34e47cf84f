from app import app
import dash
from dash import Output, Input, html
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz
import MetaTrader5 as mt5
from func_mt5 import fetch_data, calculate_log_returns
from scipy.optimize import minimize

# Currency basket definitions from mpt_allocation.py
CURRENCY_BASKETS = {
    'USD': {"EURUSD": -0.1, "GBPUSD": -0.1, "AUDUSD": -0.1, "NZDUSD": -0.1,
            "USDCAD": 0.1, "USDCHF": 0.1, "USDJPY": 0.1},
    'EUR': {"EURUSD": 0.1, "EURGBP": 0.1, "EURAUD": 0.1, "EURNZD": 0.1,
            "EURCAD": 0.1, "EURCHF": 0.1, "EURJPY": 0.1},
    'GBP': {"GBPUSD": 0.1, "EURGBP": -0.1, "GBPAUD": 0.1, "GBPNZD": 0.1,
            "GBPCAD": 0.1, "GBPCHF": 0.1, "GBPJPY": 0.1},
    'AUD': {"AUDUSD": 0.1, "EURAUD": -0.1, "GBPAUD": -0.1, "AUDNZD": 0.1,
            "AUDCAD": 0.1, "AUDCHF": 0.1, "AUDJPY": 0.1},
    'NZD': {"NZDUSD": 0.1, "EURNZD": -0.1, "GBPNZD": -0.1, "AUDNZD": -0.1,
            "NZDCAD": 0.1, "NZDCHF": 0.1, "NZDJPY": 0.1},
    'CAD': {"USDCAD": -0.1, "EURCAD": -0.1, "GBPCAD": -0.1, "AUDCAD": -0.1,
            "NZDCAD": -0.1, "CADCHF": 0.1, "CADJPY": 0.1},
    'CHF': {"USDCHF": -0.1, "EURCHF": -0.1, "GBPCHF": -0.1, "AUDCHF": -0.1,
            "NZDCHF": -0.1, "CADCHF": -0.1, "CHFJPY": 0.1},
    'JPY': {"USDJPY": -0.1, "EURJPY": -0.1, "GBPJPY": -0.1, "AUDJPY": -0.1,
            "NZDJPY": -0.1, "CADJPY": -0.1, "CHFJPY": -0.1}
}

def normalize_basket_weights(weights_dict):
    """Normalize weights to absolute sum of 1"""
    abs_sum = sum(abs(w) for w in weights_dict.values())
    if abs_sum > 0:
        return {k: v/abs_sum for k, v in weights_dict.items()}
    return weights_dict

def get_start_of_day():
    """Get start of day in trading timezone"""
    local_tz = pytz.timezone('Europe/Bucharest')
    now = datetime.now(local_tz)
    return now.replace(hour=0, minute=0, second=0, microsecond=0)

def calculate_basket_returns_since_sod():
    """Calculate log returns (not normalized) for each currency basket since start of day"""
    try:
        # Get all unique symbols from all baskets
        all_symbols = set()
        for basket_weights in CURRENCY_BASKETS.values():
            all_symbols.update(basket_weights.keys())
        all_symbols = list(all_symbols)

        # Get start of day and current time
        sod = get_start_of_day()
        now = datetime.now(pytz.timezone('Europe/Bucharest'))

        # Fetch M1 data since start of day
        market_data = fetch_data(all_symbols, timeframe=mt5.TIMEFRAME_M1,
                               start_time=sod, end_time=now)

        if not market_data:

            return {}, pd.DataFrame()

        # Calculate log returns
        log_returns = calculate_log_returns(market_data)

        # Calculate cumulative returns since SoD for each basket
        basket_returns = {}

        for basket_name, weights in CURRENCY_BASKETS.items():
            # Use original weights (not normalized) for log returns calculation
            original_weights = weights

            # Calculate basket return
            available_symbols = [sym for sym in original_weights.keys() if sym in log_returns.columns]
            if not available_symbols:
                basket_returns[basket_name] = 0.0
                continue

            # Get weights for available symbols
            weight_series = pd.Series({sym: original_weights[sym] for sym in available_symbols})

            # Calculate weighted portfolio returns using original weights
            basket_log_returns = log_returns[available_symbols].multiply(weight_series, axis=1).sum(axis=1)

            # Sum all returns since SoD to get cumulative return
            cumulative_return = basket_log_returns.sum()
            basket_returns[basket_name] = cumulative_return

        return basket_returns, log_returns

    except Exception as e:

        return {}, pd.DataFrame()

def get_currency_pair_rank(currency, target_symbol, log_returns_data, excluded_currencies):
    """Get rank of target_symbol among ALL pairs for the given currency (by absolute log return)"""
    pairs_with_returns = []

    # Get all possible other currencies (excluding the currency itself and excluded ones)
    all_currencies = ['USD', 'EUR', 'GBP', 'AUD', 'NZD', 'CAD', 'CHF', 'JPY']
    other_currencies = [c for c in all_currencies if c != currency and c not in excluded_currencies]

    for other_currency in other_currencies:
        symbol1 = f"{currency}{other_currency}"
        symbol2 = f"{other_currency}{currency}"

        if symbol1 in log_returns_data.columns:
            # Calculate cumulative log return and get absolute value
            cumulative_return = float(log_returns_data[symbol1].sum())
            pairs_with_returns.append((symbol1, abs(cumulative_return)))
        elif symbol2 in log_returns_data.columns:
            # Calculate cumulative log return and get absolute value
            cumulative_return = float(log_returns_data[symbol2].sum())
            pairs_with_returns.append((symbol2, abs(cumulative_return)))

    # Sort by absolute log return (descending)
    pairs_with_returns.sort(key=lambda x: x[1], reverse=True)

    # Find rank of target symbol
    for i, (symbol, _) in enumerate(pairs_with_returns):
        if symbol == target_symbol:
            return i + 1

    return len(pairs_with_returns) + 1  # Not found

def get_hierarchical_trading_pairs(basket_returns, log_returns_data):
    """
    Generate 9 BUY and 9 SELL recommendations using hierarchical slot assignment:

    BUY Recommendations (9 slots):
    - Top 4 pairs from the strongest currency
    - Top 3 pairs from the 2nd strongest currency
    - Top 2 pairs from the 3rd strongest currency

    SELL Recommendations (9 slots):
    - Bottom 4 pairs from the weakest currency
    - Bottom 3 pairs from the 2nd weakest currency
    - Bottom 2 pairs from the 3rd weakest currency
    """
    if not basket_returns or log_returns_data.empty:
        return [], []

    # Sort baskets by performance (strongest to weakest)
    sorted_baskets = sorted(basket_returns.items(), key=lambda x: x[1], reverse=True)

    if len(sorted_baskets) < 3:
        return [], []

    # Get currency rankings with filtering
    all_strongest = [sorted_baskets[i] for i in range(3)]
    all_weakest = [sorted_baskets[i] for i in range(-3, 0)]

    # Filter strongest currencies: exclude 2nd/3rd if they have negative returns
    strongest_currencies = []
    for i, (currency, return_val) in enumerate(all_strongest):
        if i == 0:  # Always include 1st strongest
            strongest_currencies.append(currency)
        elif return_val > 0:  # Only include 2nd/3rd if positive returns
            strongest_currencies.append(currency)

    # Filter weakest currencies: exclude 2nd/3rd if they have positive returns
    weakest_currencies = []
    for i, (currency, return_val) in enumerate(all_weakest):
        if i == 0:  # Always include 1st weakest
            weakest_currencies.append(currency)
        elif return_val < 0:  # Only include 2nd/3rd if negative returns
            weakest_currencies.append(currency)

    # Generate BUY recommendations using position-based matching
    buy_pairs = []
    if len(strongest_currencies) >= 1:
        # Top 1 vs Bottom 4 (positions 5,6,7,8)
        target_positions = [5, 6, 7, 8]  # AUD, NZD, JPY, CHF positions
        target_currencies = [sorted_baskets[i-1][0] for i in target_positions if i <= len(sorted_baskets)]
        buy_pairs.extend(get_currency_pairs_vs_targets(strongest_currencies[0], target_currencies, log_returns_data, 4, 'strongest'))

    if len(strongest_currencies) >= 2:
        # Top 2 vs Bottom 3 (positions 6,7,8)
        target_positions = [6, 7, 8]  # NZD, JPY, CHF positions
        target_currencies = [sorted_baskets[i-1][0] for i in target_positions if i <= len(sorted_baskets)]
        buy_pairs.extend(get_currency_pairs_vs_targets(strongest_currencies[1], target_currencies, log_returns_data, 3, '2nd_strongest'))

    if len(strongest_currencies) >= 3:
        # Top 3 vs Bottom 2 (positions 7,8)
        target_positions = [7, 8]  # JPY, CHF positions
        target_currencies = [sorted_baskets[i-1][0] for i in target_positions if i <= len(sorted_baskets)]
        buy_pairs.extend(get_currency_pairs_vs_targets(strongest_currencies[2], target_currencies, log_returns_data, 2, '3rd_strongest'))

    # Generate SELL recommendations using position-based matching (use actual positions, not filtered indices)
    sell_pairs = []

    # Bottom 1 (last position) vs Top 4 (positions 1,2,3,4)
    if len(sorted_baskets) >= 8:  # Need at least 8 baskets for position 8
        bottom_1_currency = sorted_baskets[-1][0]  # Last position (CHF in your example)
        target_positions = [1, 2, 3, 4]  # GBP, EUR, CAD, USD positions
        target_currencies = [sorted_baskets[i-1][0] for i in target_positions if i <= len(sorted_baskets)]
        sell_pairs.extend(get_currency_pairs_vs_targets(bottom_1_currency, target_currencies, log_returns_data, 4, 'weakest'))

    # Bottom 2 (2nd to last position) vs Top 3 (positions 1,2,3)
    if len(sorted_baskets) >= 7:  # Need at least 7 baskets for position 7
        bottom_2_currency = sorted_baskets[-2][0]  # 2nd to last position (JPY in your example)
        target_positions = [1, 2, 3]  # GBP, EUR, CAD positions
        target_currencies = [sorted_baskets[i-1][0] for i in target_positions if i <= len(sorted_baskets)]
        sell_pairs.extend(get_currency_pairs_vs_targets(bottom_2_currency, target_currencies, log_returns_data, 3, '2nd_weakest'))

    # Bottom 3 (3rd to last position) vs Top 2 (positions 1,2)
    if len(sorted_baskets) >= 6:  # Need at least 6 baskets for position 6
        bottom_3_currency = sorted_baskets[-3][0]  # 3rd to last position (NZD in your example)
        target_positions = [1, 2]  # GBP, EUR positions
        target_currencies = [sorted_baskets[i-1][0] for i in target_positions if i <= len(sorted_baskets)]
        sell_pairs.extend(get_currency_pairs_vs_targets(bottom_3_currency, target_currencies, log_returns_data, 2, '3rd_weakest'))

    # Remove duplicates and apply directional logic
    buy_pairs = resolve_pair_conflicts(buy_pairs, 'BUY', log_returns_data)
    sell_pairs = resolve_pair_conflicts(sell_pairs, 'SELL', log_returns_data)

    return buy_pairs[:9], sell_pairs[:9]

def get_currency_pairs_vs_targets(currency, target_currencies, log_returns_data, num_pairs, strength_type):
    """Get pairs for a currency only against specific target currencies (position-based matching)"""
    available_symbols = list(log_returns_data.columns)
    currency_pairs = []

    # Find all pairs containing this currency that also contain one of the target currencies
    for symbol in available_symbols:
        if currency in symbol:
            # Determine the other currency in the pair
            if symbol.startswith(currency):
                other_currency = symbol[3:]  # Remove first 3 characters (currency code)
            elif symbol.endswith(currency):
                other_currency = symbol[:3]  # Take first 3 characters
            else:
                continue  # Currency not at start or end

            # Only include if the other currency is in our target list
            if other_currency in target_currencies:
                if symbol in log_returns_data.columns:
                    # Calculate cumulative log return since start of day (like other functions)
                    log_return = log_returns_data[symbol].sum() if not log_returns_data[symbol].empty else 0

                    currency_pairs.append({
                        'symbol': symbol,
                        'other_currency': other_currency,
                        'log_return': log_return,
                        'color_code': 'normal'
                    })

    # Sort by absolute log returns (descending)
    currency_pairs.sort(key=lambda x: abs(x['log_return']), reverse=True)

    return currency_pairs[:num_pairs]

def get_currency_pairs_no_collision(currency, basket_returns, log_returns_data, num_pairs, strength_type, excluded_currencies):
    """Get top pairs for a currency based on log returns, excluding pairs with specified currencies to avoid hierarchy collisions"""
    available_symbols = list(log_returns_data.columns)
    currency_pairs = []

    # Find all pairs containing this currency but NOT the excluded currencies
    for symbol in available_symbols:
        if len(symbol) >= 6:
            base = symbol[:3]
            quote = symbol[3:6]

            # Check if this pair contains our target currency
            if currency in [base, quote]:
                # Check if this pair contains any excluded currency (collision avoidance)
                other_currency = quote if currency == base else base
                if other_currency in excluded_currencies:
                    continue

                # Calculate cumulative log return for this pair
                pair_log_return = log_returns_data[symbol].sum()

                # Determine if this currency is base or quote
                is_base = (currency == base)

                # Get rank of this pair among ALL pairs for this currency (by absolute log return)
                currency_pair_rank = get_currency_pair_rank(currency, symbol, log_returns_data, excluded_currencies)

                # Get the other currency's basket return for color coding
                other_currency_return = basket_returns.get(other_currency, 0.0)

                # Color coding logic
                color_code = 'normal'  # Default

                if strength_type in ['strongest', '2nd_strongest', '3rd_strongest']:
                    # BUY side color coding
                    if currency_pair_rank > 4:
                        # Not in top 4 absolute log returns for this currency
                        color_code = 'yellow'
                    elif other_currency_return > 0:
                        # Target currency has positive returns (should be negative for "weak" currency)
                        color_code = 'red'
                else:
                    # SELL side color coding
                    if currency_pair_rank > 4:
                        # Not in top 4 absolute log returns for this currency
                        color_code = 'lime'
                    elif other_currency_return < 0:
                        # Target currency has negative returns (should be positive for "strong" currency)
                        color_code = 'blue'

                currency_pairs.append({
                    'symbol': symbol,
                    'base': base,
                    'quote': quote,
                    'log_return': pair_log_return,
                    'abs_log_return': abs(pair_log_return),
                    'is_base': is_base,
                    'strength_type': strength_type,
                    'currency_pair_rank': currency_pair_rank,
                    'color_code': color_code,
                    'other_currency': other_currency,
                    'other_currency_return': other_currency_return
                })

    # Sort by absolute log return (best performance first)
    currency_pairs.sort(key=lambda x: x['abs_log_return'], reverse=True)

    return currency_pairs[:num_pairs]

def get_currency_pairs(currency, basket_returns, log_returns_data, num_pairs, strength_type):
    """Get top pairs for a currency based on log returns"""
    available_symbols = list(log_returns_data.columns)
    currency_pairs = []

    # Find all pairs containing this currency
    for symbol in available_symbols:
        if len(symbol) >= 6:
            base = symbol[:3]
            quote = symbol[3:6]

            if currency in [base, quote]:
                # Calculate cumulative log return for this pair
                pair_log_return = log_returns_data[symbol].sum()

                # Determine if this currency is base or quote
                is_base = (currency == base)

                # For color coding: check if this pair would be in top vs all currencies
                other_currencies = [c for c in ['USD', 'EUR', 'GBP', 'AUD', 'NZD', 'CAD', 'CHF', 'JPY'] if c != currency]
                vs_all_rank = get_pair_rank_vs_all(currency, symbol, log_returns_data, other_currencies)

                # Check if this pair would be in top vs weak currencies (for strongest) or strong currencies (for weakest)
                if strength_type in ['strongest', '2nd_strongest', '3rd_strongest']:
                    weak_currencies = sorted(basket_returns.items(), key=lambda x: x[1])[:4]  # 4 weakest
                    weak_currency_names = [c[0] for c in weak_currencies]
                    vs_target_rank = get_pair_rank_vs_target(currency, symbol, log_returns_data, weak_currency_names)
                else:  # weakest currencies
                    strong_currencies = sorted(basket_returns.items(), key=lambda x: x[1], reverse=True)[:4]  # 4 strongest
                    strong_currency_names = [c[0] for c in strong_currencies]
                    vs_target_rank = get_pair_rank_vs_target(currency, symbol, log_returns_data, strong_currency_names)

                # Color coding: different color if not in both top lists
                color_code = 'normal'
                if vs_all_rank <= num_pairs and vs_target_rank > num_pairs:
                    color_code = 'vs_all_only'
                elif vs_all_rank > num_pairs and vs_target_rank <= num_pairs:
                    color_code = 'vs_target_only'

                currency_pairs.append({
                    'symbol': symbol,
                    'base': base,
                    'quote': quote,
                    'log_return': pair_log_return,
                    'abs_log_return': abs(pair_log_return),
                    'is_base': is_base,
                    'strength_type': strength_type,
                    'vs_all_rank': vs_all_rank,
                    'vs_target_rank': vs_target_rank,
                    'color_code': color_code
                })

    # Sort by absolute log return (best performance first)
    currency_pairs.sort(key=lambda x: x['abs_log_return'], reverse=True)

    return currency_pairs[:num_pairs]

def get_pair_rank_vs_all(currency, symbol, log_returns_data, other_currencies):
    """Get rank of this pair when currency is compared vs all other currencies"""
    all_pairs = []
    for other_currency in other_currencies:
        for sym in log_returns_data.columns:
            if len(sym) >= 6:
                base = sym[:3]
                quote = sym[3:6]
                if currency in [base, quote] and other_currency in [base, quote]:
                    pair_return = abs(log_returns_data[sym].sum())
                    all_pairs.append((sym, pair_return))

    # Sort by absolute return
    all_pairs.sort(key=lambda x: x[1], reverse=True)

    # Find rank of our symbol
    for i, (sym, _) in enumerate(all_pairs):
        if sym == symbol:
            return i + 1
    return len(all_pairs) + 1

def get_pair_rank_vs_target(currency, symbol, log_returns_data, target_currencies):
    """Get rank of this pair when currency is compared vs target currencies only"""
    target_pairs = []
    for target_currency in target_currencies:
        for sym in log_returns_data.columns:
            if len(sym) >= 6:
                base = sym[:3]
                quote = sym[3:6]
                if currency in [base, quote] and target_currency in [base, quote]:
                    pair_return = abs(log_returns_data[sym].sum())
                    target_pairs.append((sym, pair_return))

    # Sort by absolute return
    target_pairs.sort(key=lambda x: x[1], reverse=True)

    # Find rank of our symbol
    for i, (sym, _) in enumerate(target_pairs):
        if sym == symbol:
            return i + 1
    return len(target_pairs) + 1

def resolve_pair_conflicts(pairs, direction, log_returns_data):
    """Resolve conflicts when same pair appears in multiple lists"""
    seen_pairs = {}
    resolved_pairs = []

    for pair_info in pairs:
        symbol = pair_info['symbol']
        log_return = log_returns_data[symbol].sum()

        if symbol not in seen_pairs:
            # Determine final direction based on log return sign and direction type
            if direction == 'BUY':
                # For BUY: use positive log returns (currency strengthening)
                if log_return > 0:
                    pair_info['final_return'] = log_return
                    pair_info['direction'] = 'BUY'
                    resolved_pairs.append(pair_info)
                    seen_pairs[symbol] = True
            else:  # SELL
                # For SELL: use negative log returns (currency weakening)
                if log_return < 0:
                    pair_info['final_return'] = log_return
                    pair_info['direction'] = 'SELL'
                    resolved_pairs.append(pair_info)
                    seen_pairs[symbol] = True

    return resolved_pairs

def find_best_pair_with_return(strong_currency, weak_currency, available_symbols, log_returns_data):
    """Find the best trading pair between two currencies with actual log return data"""
    # Find pairs that involve both currencies
    possible_pairs = []
    for symbol in available_symbols:
        if strong_currency in symbol and weak_currency in symbol:
            possible_pairs.append(symbol)

    if not possible_pairs:
        # If no direct pair exists, try common pairs
        common_pairs = [
            f"{strong_currency}{weak_currency}",
            f"{weak_currency}{strong_currency}"
        ]
        for pair in common_pairs:
            if pair in available_symbols:
                possible_pairs.append(pair)

    # Find the pair with highest absolute log return
    best_pair_info = None
    best_abs_return = 0

    for pair in possible_pairs:
        if pair in log_returns_data.columns:
            # Calculate cumulative log return since SoD
            pair_log_return = log_returns_data[pair].sum()
            abs_return = abs(pair_log_return)

            if abs_return > best_abs_return:
                best_abs_return = abs_return
                best_pair_info = {
                    'symbol': pair,
                    'log_return': pair_log_return,
                    'strong_currency': strong_currency,
                    'weak_currency': weak_currency
                }

    return best_pair_info

def find_best_pair(strong_currency, weak_currency, basket_returns):
    """Legacy function for backward compatibility"""
    # Get all symbols that exist in our baskets
    all_symbols = set()
    for basket_weights in CURRENCY_BASKETS.values():
        all_symbols.update(basket_weights.keys())

    # Find pairs that involve both currencies
    possible_pairs = []
    for symbol in all_symbols:
        if strong_currency in symbol and weak_currency in symbol:
            possible_pairs.append(symbol)

    if not possible_pairs:
        # If no direct pair exists, try common pairs
        common_pairs = [
            f"{strong_currency}{weak_currency}",
            f"{weak_currency}{strong_currency}"
        ]
        for pair in common_pairs:
            if pair in all_symbols:
                possible_pairs.append(pair)

    return possible_pairs[0] if possible_pairs else None

@app.callback(
    Output('basket-analysis-table', 'children'),
    [Input('basket-analysis-interval', 'n_intervals')]
)
def update_basket_analysis(n_intervals):
    """Update the basket analysis table with current data"""
    try:
        # Calculate basket returns since SoD
        basket_returns, log_returns_data = calculate_basket_returns_since_sod()

        if not basket_returns:

            return html.Div([
                html.P("No data available - Check MT5 connection", style={'color': 'yellow'}),
                html.P(f"Callback triggered at interval {n_intervals}", style={'color': 'lightgray', 'fontSize': '12px'})
            ])

        # Get hierarchical trading pairs (9 BUY, 9 SELL)
        buy_pairs_info, sell_pairs_info = get_hierarchical_trading_pairs(basket_returns, log_returns_data)

        # Sort baskets by performance for display
        sorted_baskets = sorted(basket_returns.items(), key=lambda x: x[1], reverse=True)

        # Create the table
        table_style = {
            'width': '100%',
            'border': '1px solid white',
            'borderCollapse': 'collapse',
            'backgroundColor': '#1e1e1e'
        }

        cell_style = {
            'border': '1px solid white',
            'padding': '8px',
            'textAlign': 'center',
            'color': 'white'
        }

        header_style = {
            **cell_style,
            'backgroundColor': '#333',
            'fontWeight': 'bold'
        }

        # Create table rows
        table_rows = [
            html.Tr([
                html.Th("Buy Pairs", style=header_style),
                html.Th("Buy Return", style=header_style),
                html.Th("Sell Pairs", style=header_style),
                html.Th("Sell Return", style=header_style),
                html.Th("Basket", style=header_style),
                html.Th("Basket Return SoD", style=header_style)
            ])
        ]

        # Add data rows
        max_rows = max(len(buy_pairs_info), len(sell_pairs_info), len(sorted_baskets))

        for i in range(max_rows):
            # Buy pair info
            if i < len(buy_pairs_info):
                buy_info = buy_pairs_info[i]
                buy_pair = buy_info['symbol']
                buy_return = f"{buy_info.get('final_return', buy_info['log_return']):.4f}"

                # Color coding for buy pairs
                if buy_info.get('color_code') == 'yellow':
                    buy_pair_color = 'yellow'  # Not in top 4 absolute log returns for this currency
                elif buy_info.get('color_code') == 'red':
                    buy_pair_color = 'red'  # Target currency has positive returns (should be negative)
                else:
                    buy_pair_color = 'lightblue'  # Normal

                buy_return_style = {**cell_style, 'color': 'lightgreen'} if buy_info.get('final_return', buy_info['log_return']) > 0 else {**cell_style, 'color': 'lightcoral'}
                buy_style = {**cell_style, 'color': buy_pair_color, 'fontWeight': 'bold'}
            else:
                buy_pair = ""
                buy_return = ""
                buy_return_style = cell_style
                buy_style = cell_style

            # Sell pair info
            if i < len(sell_pairs_info):
                sell_info = sell_pairs_info[i]
                sell_pair = sell_info['symbol']
                sell_return = f"{sell_info.get('final_return', sell_info['log_return']):.4f}"

                # Color coding for sell pairs
                if sell_info.get('color_code') == 'lime':
                    sell_pair_color = 'lime'  # Not in top 4 absolute log returns for this currency
                elif sell_info.get('color_code') == 'blue':
                    sell_pair_color = 'lightblue'  # Target currency has negative returns (should be positive)
                else:
                    sell_pair_color = 'orange'  # Normal sell color

                sell_return_style = {**cell_style, 'color': 'lightgreen'} if sell_info.get('final_return', sell_info['log_return']) > 0 else {**cell_style, 'color': 'lightcoral'}
                sell_style = {**cell_style, 'color': sell_pair_color, 'fontWeight': 'bold'}
            else:
                sell_pair = ""
                sell_return = ""
                sell_return_style = cell_style
                sell_style = cell_style

            # Basket info
            if i < len(sorted_baskets):
                basket_name, basket_return = sorted_baskets[i]
                basket_cell = f"{basket_name}"
                basket_return_cell = f"{basket_return:.4f}"

                # Color code based on performance
                if basket_return > 0:
                    basket_return_style = {**cell_style, 'color': 'lightgreen'}
                    basket_style = {**cell_style, 'color': 'lightgreen', 'fontWeight': 'bold'}
                else:
                    basket_return_style = {**cell_style, 'color': 'lightcoral'}
                    basket_style = {**cell_style, 'color': 'lightcoral', 'fontWeight': 'bold'}
            else:
                basket_cell = ""
                basket_return_cell = ""
                basket_return_style = cell_style
                basket_style = cell_style

            row = html.Tr([
                html.Td(buy_pair, style=buy_style),
                html.Td(buy_return, style=buy_return_style),
                html.Td(sell_pair, style=sell_style),
                html.Td(sell_return, style=sell_return_style),
                html.Td(basket_cell, style=basket_style),
                html.Td(basket_return_cell, style=basket_return_style)
            ])
            table_rows.append(row)

        table = html.Table(table_rows, style=table_style)

        return html.Div([
            table,
            html.P(f"Last updated: {datetime.now(pytz.timezone('Europe/Bucharest')).strftime('%H:%M:%S')}",
                   style={'color': 'lightgray', 'fontSize': '12px', 'marginTop': '5px'})
        ])

    except Exception as e:
        import traceback
        traceback.print_exc()
        return html.Div([
            html.P(f"Error: {str(e)}", style={'color': 'red'}),
            html.P(f"Callback triggered at interval {n_intervals}", style={'color': 'lightgray', 'fontSize': '12px'})
        ])

def optimize_portfolio_cf_sharpe(symbols, log_returns_data):
    """
    Optimize portfolio using CF Sharpe ratio (Modified Sharpe with Cornish-Fisher adjustment)
    Uses minute-level data directly like other MPT optimizations
    Returns normalized weights in MPT format
    """
    try:
        if not symbols or log_returns_data.empty:
            return {}

        # Filter symbols that exist in data
        available_symbols = [s for s in symbols if s in log_returns_data.columns]
        if not available_symbols:
            return {}

        # Get returns for available symbols - use minute data directly
        returns_data = log_returns_data[available_symbols]

        # Convert log returns to arithmetic returns like main MPT
        from func_mt5 import convert_log_to_arithmetic_returns
        arithmetic_returns = convert_log_to_arithmetic_returns(returns_data)

        # Calculate mean returns and covariance matrix like main MPT
        mean_returns = arithmetic_returns.mean()
        cov_matrix = arithmetic_returns.cov()

        def calculate_cf_sharpe(weights):
            """Calculate negative CF Sharpe (Modified Sharpe) for minimization using minute data"""
            try:
                # Calculate portfolio returns using minute data
                portfolio_returns = returns_data.dot(weights)

                if len(portfolio_returns) < 2:
                    return 1000  # High penalty

                # Use the modified Sharpe ratio calculation from ratio_calcs
                from ratio_calcs_rust_wrapper import neg_modified_sharpe_ratio

                # Calculate mean returns and covariance matrix
                mean_returns = arithmetic_returns.mean().values
                cov_matrix = arithmetic_returns.cov().values

                # Convert portfolio returns to 2D array for modified Sharpe calculation
                returns_2d = portfolio_returns.values.reshape(-1, 1)

                # Calculate negative modified Sharpe ratio
                neg_mod_sharpe = neg_modified_sharpe_ratio(
                    weights, mean_returns, cov_matrix, returns_2d, 0.0
                )

                if not np.isfinite(neg_mod_sharpe):
                    return 1000

                return neg_mod_sharpe  # Already negative for minimization

            except Exception as e:
                return 1000

        # Initial equal weights
        n_assets = len(available_symbols)
        initial_weights = np.array([1.0 / n_assets] * n_assets)

        # Use standard constraints like main MPT (weights sum to 1)
        constraints = [{'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0}]
        bounds = [(-1.0, 1.0) for _ in range(n_assets)]

        # Optimize
        result = minimize(
            calculate_cf_sharpe,
            initial_weights,
            method='SLSQP',
            bounds=bounds,
            constraints=constraints,
            options={'maxiter': 1000, 'ftol': 1e-9}
        )

        if result.success:
            weights = result.x
        else:
            # Fallback to equal weights
            weights = initial_weights

        # Normalize to ensure abs sum = 1 (MPT format)
        abs_sum = np.sum(np.abs(weights))
        if abs_sum > 0:
            weights = weights / abs_sum

        return {symbol: float(weight) for symbol, weight in zip(available_symbols, weights)}

    except Exception as e:

        # Fallback: equal weights
        if symbols:
            weight = 1.0 / len(symbols)
            return {symbol: weight for symbol in symbols}
        return {}

def generate_hierarchical_portfolio(buy_pairs_info, sell_pairs_info):
    """
    Generate portfolio from currency basket hierarchical selection:
    - For each currency basket ranked by performance, sort components by abs log returns
    - Take top N pairs from each basket (3 from strongest, 2 from 2nd strongest, 1 from 3rd strongest, etc.)
    - Skip middle 2 baskets, then same pattern for weakest baskets
    - If same pairs appear from different baskets, sum their log returns
    - Normalize weights and optimize with CF Sharpe (Modified Sharpe)
    """
    try:
        # Get current log returns data
        basket_returns, log_returns_data = calculate_basket_returns_since_sod()
        if log_returns_data is None or log_returns_data.empty:
            return {}

        # Sort baskets by returns (strongest to weakest)
        sorted_baskets = sorted(basket_returns.items(), key=lambda x: x[1], reverse=True)

        # Define selection counts: top 3, 2nd 2, 3rd 1, skip middle 2, bottom 3rd 1, 2nd 2, bottom 3
        selection_counts = [3, 2, 1, 0, 0, 1, 2, 3]  # Skip middle 2 baskets

        combined_log_returns = {}

        # Process each basket according to hierarchy
        for i, (currency, basket_return) in enumerate(sorted_baskets):
            if i >= len(selection_counts):
                break

            count = selection_counts[i]
            if count == 0:
                continue  # Skip middle baskets

            # Get pairs from this basket and their absolute log returns
            basket_pairs = CURRENCY_BASKETS[currency]
            pair_abs_returns = []

            for symbol, basket_weight in basket_pairs.items():
                if symbol in log_returns_data.columns:
                    symbol_log_return = float(log_returns_data[symbol].sum())
                    abs_return = abs(symbol_log_return)
                    pair_abs_returns.append((symbol, symbol_log_return, abs_return, basket_weight))

            # Sort by absolute log returns (highest first)
            pair_abs_returns.sort(key=lambda x: x[2], reverse=True)

            # Take top N pairs with original log returns (no sign manipulation yet)
            for j in range(min(count, len(pair_abs_returns))):
                symbol, original_log_return, abs_return, basket_weight = pair_abs_returns[j]

                # Just use the original log return - we'll apply BUY/SELL logic after combining
                contribution = original_log_return

                # Sum contributions if pair appears multiple times
                if symbol in combined_log_returns:
                    combined_log_returns[symbol] += contribution
                else:
                    combined_log_returns[symbol] = contribution

        # Convert combined log returns to initial weights and optimize with CF Sharpe (Modified Sharpe)
        if combined_log_returns:
            # Normalize the combined log returns to abs sum = 1 as initial weights
            abs_sum = sum(abs(ret) for ret in combined_log_returns.values())
            if abs_sum > 0:
                initial_weights = {symbol: ret / abs_sum for symbol, ret in combined_log_returns.items()}
                # Optimize with CF Sharpe (Modified Sharpe)
                symbols = list(initial_weights.keys())
                optimized_weights = optimize_portfolio_cf_sharpe(symbols, log_returns_data)
                # Return both normalized and optimized versions
                return initial_weights, optimized_weights

        return {}, {}

    except Exception as e:

        return {}

def format_mpt_portfolio(weights_dict):
    """Format portfolio weights in MPT format: SYMBOL:weight,SYMBOL:weight,..."""
    if not weights_dict:
        return ""

    # Sort by absolute weight (descending) for better readability
    sorted_items = sorted(weights_dict.items(), key=lambda x: abs(x[1]), reverse=True)

    # Format as SYMBOL:weight
    formatted_pairs = []
    for symbol, weight in sorted_items:
        if abs(weight) > 0.001:  # Only include significant weights
            formatted_pairs.append(f"{symbol}:{weight:.3f}")

    return ",".join(formatted_pairs)

@app.callback(
    [Output('basket-portfolio-all', 'value'),
     Output('basket-portfolio-filtered', 'value'),
     Output('basket-portfolio-hierarchical', 'value'),
     Output('basket-portfolio-hierarchical-normalized', 'value')],
    [Input('basket-analysis-interval', 'n_intervals')]
)
def update_basket_portfolios(n_intervals):
    """Update the four MPT portfolio textareas based on basket analysis"""
    try:
        # Calculate basket returns and get trading pairs
        basket_returns, log_returns_data = calculate_basket_returns_since_sod()

        if not basket_returns or log_returns_data.empty:
            return "", "", "", ""

        # Get hierarchical trading pairs
        buy_pairs_info, sell_pairs_info = get_hierarchical_trading_pairs(basket_returns, log_returns_data)

        if not buy_pairs_info and not sell_pairs_info:
            return "", "", "", ""

        # 1. All recommended pairs - use log returns as initial weights, then CF Sharpe optimize
        all_initial_weights = {}
        for pair in buy_pairs_info:
            symbol = pair['symbol']
            log_return = pair['log_return']
            all_initial_weights[symbol] = log_return  # BUY pairs get positive weights
        for pair in sell_pairs_info:
            symbol = pair['symbol']
            log_return = pair['log_return']
            all_initial_weights[symbol] = -log_return  # SELL pairs get negative weights

        # Normalize to abs sum = 1, then optimize
        if all_initial_weights:
            abs_sum = sum(abs(w) for w in all_initial_weights.values())
            if abs_sum > 0:
                all_initial_weights = {s: w/abs_sum for s, w in all_initial_weights.items()}
            all_symbols = list(all_initial_weights.keys())
            all_weights = optimize_portfolio_cf_sharpe(all_symbols, log_returns_data)
            portfolio_all = format_mpt_portfolio(all_weights)
        else:
            portfolio_all = ""

        # 2. All recommended except color-coded - just normalized weights (no CF Sharpe optimization)
        filtered_weights = {}
        for pair in buy_pairs_info + sell_pairs_info:
            color_code = pair.get('color_code', 'normal')
            if color_code == 'normal':  # Only include normal (non-color-coded) pairs
                symbol = pair['symbol']
                log_return = pair['log_return']
                # Use log_return directly - BUY pairs already have positive values, SELL pairs already have negative values
                filtered_weights[symbol] = log_return

        # Just normalize to abs sum = 1 (no optimization)
        if filtered_weights:
            abs_sum = sum(abs(w) for w in filtered_weights.values())
            if abs_sum > 0:
                filtered_weights = {s: w/abs_sum for s, w in filtered_weights.items()}
            portfolio_filtered = format_mpt_portfolio(filtered_weights)
        else:
            portfolio_filtered = ""

        # 3. Hierarchical selection (CF Sharpe optimized)
        # 4. Hierarchical selection (normalized only)
        hierarchical_normalized, hierarchical_optimized = generate_hierarchical_portfolio(buy_pairs_info, sell_pairs_info)
        portfolio_hierarchical_optimized = format_mpt_portfolio(hierarchical_optimized)
        portfolio_hierarchical_normalized = format_mpt_portfolio(hierarchical_normalized)

        return portfolio_all, portfolio_filtered, portfolio_hierarchical_optimized, portfolio_hierarchical_normalized

    except Exception as e:
        import traceback
        traceback.print_exc()
        return "", "", "", ""
