C:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\build\pyo3-build-config-925ed363980d5dea\build_script_build-925ed363980d5dea.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\build.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\impl_.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\errors.rs

C:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\build\pyo3-build-config-925ed363980d5dea\build_script_build-925ed363980d5dea.exe: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\build.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\impl_.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\errors.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\build.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\impl_.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\errors.rs:

# env-dep:CARGO_PKG_VERSION=0.25.1
