#!/usr/bin/env python3
"""
Detailed accuracy investigation for Rust vs Python efficient frontier implementations
"""

import numpy as np
from efficient_frontier_rust_wrapper import (
    efficient_frontier_upper_hull, 
    efficient_frontier_upper_hull_python
)

def generate_simple_test_case():
    """Generate a simple, predictable test case"""
    candidates = [
        {'risk': 0.01, 'return': 0.001, 'cvar_95': 0.012},
        {'risk': 0.02, 'return': 0.002, 'cvar_95': 0.024},
        {'risk': 0.03, 'return': 0.003, 'cvar_95': 0.036},
        {'risk': 0.015, 'return': 0.0025, 'cvar_95': 0.018},
        {'risk': 0.025, 'return': 0.0015, 'cvar_95': 0.030},
    ]
    return candidates

def test_simple_case():
    """Test with a simple, predictable case"""
    print("🔍 Testing Simple Case")
    print("=" * 40)
    
    candidates = generate_simple_test_case()
    
    print("Input candidates:")
    for i, c in enumerate(candidates):
        print(f"  {i}: risk={c['risk']:.3f}, return={c['return']:.4f}")
    
    # Test both implementations
    rust_hull = efficient_frontier_upper_hull(candidates)
    python_hull = efficient_frontier_upper_hull_python(candidates)
    
    print(f"\nRust hull ({len(rust_hull)} points):")
    for i, point in enumerate(rust_hull):
        print(f"  {i}: ({point[0]:.3f}, {point[1]:.4f})")
    
    print(f"\nPython hull ({len(python_hull)} points):")
    for i, point in enumerate(python_hull):
        print(f"  {i}: ({point[0]:.3f}, {point[1]:.4f})")
    
    # Check if results are similar (allowing for floating point precision)
    def points_similar(p1, p2, tolerance=1e-10):
        return abs(p1[0] - p2[0]) < tolerance and abs(p1[1] - p2[1]) < tolerance
    
    print(f"\nComparison:")
    if len(rust_hull) == len(python_hull):
        all_similar = True
        for i, (r_point, p_point) in enumerate(zip(rust_hull, python_hull)):
            similar = points_similar(r_point, p_point)
            print(f"  Point {i}: {'✓' if similar else '✗'} "
                  f"Rust{r_point} vs Python{p_point}")
            if not similar:
                all_similar = False
        
        if all_similar:
            print("✅ Results are mathematically equivalent!")
        else:
            print("❌ Results differ beyond floating point precision")
    else:
        print(f"❌ Different number of points: Rust={len(rust_hull)}, Python={len(python_hull)}")
    
    return rust_hull, python_hull

def test_edge_cases():
    """Test edge cases"""
    print("\n🔍 Testing Edge Cases")
    print("=" * 40)
    
    # Test case 1: Single point
    print("Test 1: Single point")
    single_point = [{'risk': 0.01, 'return': 0.001, 'cvar_95': 0.012}]
    rust_single = efficient_frontier_upper_hull(single_point)
    python_single = efficient_frontier_upper_hull_python(single_point)
    print(f"  Rust: {rust_single}")
    print(f"  Python: {python_single}")
    print(f"  Match: {'✓' if rust_single == python_single else '✗'}")
    
    # Test case 2: Two points
    print("\nTest 2: Two points")
    two_points = [
        {'risk': 0.01, 'return': 0.001, 'cvar_95': 0.012},
        {'risk': 0.02, 'return': 0.002, 'cvar_95': 0.024}
    ]
    rust_two = efficient_frontier_upper_hull(two_points)
    python_two = efficient_frontier_upper_hull_python(two_points)
    print(f"  Rust: {rust_two}")
    print(f"  Python: {python_two}")
    print(f"  Match: {'✓' if rust_two == python_two else '✗'}")
    
    # Test case 3: Collinear points
    print("\nTest 3: Collinear points")
    collinear = [
        {'risk': 0.01, 'return': 0.001, 'cvar_95': 0.012},
        {'risk': 0.02, 'return': 0.002, 'cvar_95': 0.024},
        {'risk': 0.03, 'return': 0.003, 'cvar_95': 0.036}
    ]
    rust_collinear = efficient_frontier_upper_hull(collinear)
    python_collinear = efficient_frontier_upper_hull_python(collinear)
    print(f"  Rust: {rust_collinear}")
    print(f"  Python: {python_collinear}")
    print(f"  Match: {'✓' if rust_collinear == python_collinear else '✗'}")

def investigate_algorithm_differences():
    """Investigate potential algorithm differences"""
    print("\n🔍 Investigating Algorithm Differences")
    print("=" * 40)
    
    # Create a test case where we know the expected result
    candidates = [
        {'risk': 0.01, 'return': 0.003, 'cvar_95': 0.012},  # High return, low risk (optimal)
        {'risk': 0.05, 'return': 0.001, 'cvar_95': 0.060},  # High risk, low return (suboptimal)
        {'risk': 0.02, 'return': 0.002, 'cvar_95': 0.024},  # Medium
        {'risk': 0.03, 'return': 0.0025, 'cvar_95': 0.036}, # Medium
        {'risk': 0.015, 'return': 0.0028, 'cvar_95': 0.018}, # Good risk/return ratio
    ]
    
    print("Test candidates (risk, return):")
    for i, c in enumerate(candidates):
        print(f"  {i}: ({c['risk']:.3f}, {c['return']:.4f})")
    
    # Manual calculation of expected efficient frontier
    points = [(c['risk'], c['return']) for c in candidates]
    print(f"\nPoints for convex hull: {points}")
    
    # Sort points by risk (x-coordinate)
    sorted_points = sorted(points, key=lambda p: (p[0], p[1]))
    print(f"Sorted points: {sorted_points}")
    
    # The efficient frontier should include points that are not dominated
    # A point (r1, ret1) dominates (r2, ret2) if r1 <= r2 and ret1 >= ret2
    efficient_points = []
    for point in sorted_points:
        is_efficient = True
        for other in sorted_points:
            if other != point:
                # Check if 'other' dominates 'point'
                if other[0] <= point[0] and other[1] >= point[1] and (other[0] < point[0] or other[1] > point[1]):
                    is_efficient = False
                    break
        if is_efficient:
            efficient_points.append(point)
    
    print(f"Expected efficient points: {efficient_points}")
    
    # Test both implementations
    rust_hull = efficient_frontier_upper_hull(candidates)
    python_hull = efficient_frontier_upper_hull_python(candidates)
    
    print(f"\nRust result: {rust_hull}")
    print(f"Python result: {python_hull}")
    
    # Check which points are included
    rust_set = set(rust_hull)
    python_set = set(python_hull)
    expected_set = set(efficient_points)
    
    print(f"\nSet comparison:")
    print(f"  Rust matches expected: {'✓' if rust_set == expected_set else '✗'}")
    print(f"  Python matches expected: {'✓' if python_set == expected_set else '✗'}")
    print(f"  Rust matches Python: {'✓' if rust_set == python_set else '✗'}")
    
    if rust_set != python_set:
        print(f"  Rust only: {rust_set - python_set}")
        print(f"  Python only: {python_set - rust_set}")
        print(f"  Common: {rust_set & python_set}")

def main():
    print("🔬 Detailed Accuracy Investigation")
    print("=" * 50)
    
    test_simple_case()
    test_edge_cases()
    investigate_algorithm_differences()
    
    print("\n" + "=" * 50)
    print("Investigation complete!")

if __name__ == "__main__":
    main()
