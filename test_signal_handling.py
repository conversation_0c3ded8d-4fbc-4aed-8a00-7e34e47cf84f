#!/usr/bin/env python3
"""
Test script to verify signal handling and graceful shutdown functionality.
This script simulates the optimization process to test interrupt handling.
"""

import signal
import sys
import time
import threading
import gc
from joblib import Parallel, delayed
import pandas as pd
import numpy as np

# Global variables for graceful shutdown (same as in matrix23.py)
_shutdown_requested = False
_active_parallel_jobs = []
_shutdown_lock = threading.Lock()

def signal_handler(signum, frame):
    """Handle interrupt signals gracefully"""
    global _shutdown_requested
    
    with _shutdown_lock:
        if _shutdown_requested:
            print("\nForced shutdown requested. Terminating immediately...")
            sys.exit(1)
        
        _shutdown_requested = True
        print(f"\nShutdown signal received (signal {signum}). Initiating graceful shutdown...")
        print("Press Ctrl+C again to force immediate termination.")
        
        # Cancel active parallel jobs
        for job in _active_parallel_jobs:
            try:
                if hasattr(job, '_pool') and job._pool is not None:
                    print("Terminating parallel job pool...")
                    job._pool.terminate()
                    job._pool.join(timeout=5)
            except Exception as e:
                print(f"Error terminating parallel job: {e}")
        
        # Clear pandas caches to prevent export errors
        try:
            gc.collect()
            print("Memory cleanup completed")
        except Exception as e:
            print(f"Error during memory cleanup: {e}")
        
        print("Graceful shutdown completed.")
        sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
if hasattr(signal, 'SIGTERM'):
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal

def create_parallel_executor(n_jobs=4):
    """Create a new Parallel instance for testing"""
    global _shutdown_requested, _active_parallel_jobs
    
    # Check if shutdown was requested
    if _shutdown_requested:
        raise InterruptedError("Shutdown requested, cancelling parallel execution")
    
    parallel_job = Parallel(
        n_jobs=n_jobs,
        verbose=10,
        prefer="processes",
        backend="loky",
        batch_size="auto",
        max_nbytes='10M'
    )
    
    # Track active parallel jobs for cleanup
    with _shutdown_lock:
        _active_parallel_jobs.append(parallel_job)
    
    return parallel_job

def safe_parallel_execute(parallel_job, delayed_tasks):
    """Execute parallel tasks with proper cleanup and error handling"""
    global _shutdown_requested, _active_parallel_jobs
    
    try:
        # Check if shutdown was requested before starting
        if _shutdown_requested:
            raise InterruptedError("Shutdown requested, cancelling execution")
        
        # Execute the parallel tasks
        results = parallel_job(delayed_tasks)
        
        return results
        
    except (KeyboardInterrupt, InterruptedError) as e:
        print(f"Parallel execution interrupted: {e}")
        # Force cleanup of the parallel job
        try:
            if hasattr(parallel_job, '_pool') and parallel_job._pool is not None:
                parallel_job._pool.terminate()
                parallel_job._pool.join(timeout=2)
        except Exception as cleanup_error:
            print(f"Error during parallel job cleanup: {cleanup_error}")
        raise
        
    finally:
        # Remove from active jobs list
        with _shutdown_lock:
            if parallel_job in _active_parallel_jobs:
                _active_parallel_jobs.remove(parallel_job)

def simulate_heavy_computation(data_size):
    """Simulate a heavy computation that takes time"""
    print(f"Processing data of size {data_size}...")
    
    # Create some pandas DataFrames to simulate the real workload
    df = pd.DataFrame(np.random.randn(data_size, 10))
    
    # Simulate some computation
    for i in range(100):
        if _shutdown_requested:
            print(f"Computation {data_size} interrupted")
            return None
        
        # Simulate work
        result = df.mean().sum()
        time.sleep(0.01)  # Small delay to make it interruptible
    
    print(f"Completed processing data of size {data_size}")
    return f"Result for {data_size}: {result:.4f}"

def test_signal_handling():
    """Test the signal handling with simulated optimization"""
    print("Starting signal handling test...")
    print("This will simulate the optimization process.")
    print("Press Ctrl+C to test graceful shutdown.")
    print("Press Ctrl+C twice quickly to test forced shutdown.")
    print("-" * 50)
    
    try:
        # Simulate the optimization loop
        data_sizes = [1000, 2000, 3000, 4000, 5000] * 10  # Repeat to make it longer
        
        print(f"Processing {len(data_sizes)} combinations...")
        
        # Create chunks like in the real optimization
        chunk_size = 3
        data_chunks = []
        
        for i in range(0, len(data_sizes), chunk_size):
            # Check for shutdown request during chunk preparation
            if _shutdown_requested:
                raise InterruptedError("Shutdown requested during chunk preparation")
                
            chunk_end = min(i + chunk_size, len(data_sizes))
            chunk_data = data_sizes[i:chunk_end]
            data_chunks.append(chunk_data)
        
        # Check for shutdown request before starting parallel processing
        if _shutdown_requested:
            raise InterruptedError("Shutdown requested before parallel processing")
        
        # Process chunks using parallel executor
        parallel_executor = create_parallel_executor(n_jobs=4)
        delayed_tasks = [delayed(simulate_heavy_computation)(size) for chunk in data_chunks for size in chunk]
        
        print("Starting parallel processing...")
        batch_results = safe_parallel_execute(parallel_executor, delayed_tasks)
        
        print(f"Completed processing. Got {len(batch_results)} results.")
        
    except (KeyboardInterrupt, InterruptedError) as e:
        print(f"Test interrupted: {e}")
        return False
    except Exception as e:
        print(f"Test failed with error: {e}")
        return False
    
    print("Test completed successfully!")
    return True

if __name__ == "__main__":
    print("Signal Handling Test for MPT Optimization")
    print("=" * 50)
    
    success = test_signal_handling()
    
    if success:
        print("\n✓ Signal handling test passed!")
    else:
        print("\n✗ Signal handling test failed!")
    
    print("\nTest completed.")
