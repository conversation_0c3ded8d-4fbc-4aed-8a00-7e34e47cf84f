#!/usr/bin/env python3
"""
Test integration of Rust efficient frontier functions in matrix23.py
"""

import sys
import time
import numpy as np

# Test the import and function availability
try:
    from efficient_frontier_rust_wrapper import efficient_frontier_upper_hull, efficient_frontier_upper_hull_cvar, get_rust_status
    print("✅ Successfully imported Rust efficient frontier functions")
    
    # Check Rust status
    status = get_rust_status()
    print(f"Rust Available: {status['rust_available']}")
    print(f"Functions: {status['functions_available']}")
    
except ImportError as e:
    print(f"❌ Failed to import Rust functions: {e}")
    sys.exit(1)

# Generate test portfolio candidates similar to MPT data
def generate_test_candidates(n=1000):
    """Generate test portfolio candidates"""
    np.random.seed(42)
    candidates = []
    
    for i in range(n):
        risk = np.random.uniform(0.01, 0.05)  # 1% to 5% risk
        return_val = np.random.uniform(0.001, 0.003)  # 0.1% to 0.3% return
        cvar_95 = risk * np.random.uniform(1.2, 2.0)  # CVaR typically higher than risk
        
        candidate = {
            'risk': risk,
            'return': return_val,
            'cvar_95': cvar_95,
            'combo': f'Portfolio_{i}',
            'weights': [0.33, 0.33, 0.34],
            'optimization': 'MaxSharpe'
        }
        candidates.append(candidate)
    
    return candidates

def test_rust_integration():
    """Test the Rust integration with realistic data"""
    print("\n🧪 Testing Rust Integration with Portfolio Data")
    print("=" * 50)
    
    # Generate test data
    candidates = generate_test_candidates(2000)  # Simulate increased load
    print(f"Generated {len(candidates)} test portfolio candidates")
    
    # Test standard efficient frontier
    print("\n1. Testing Standard Efficient Frontier:")
    start_time = time.time()
    hull_points = efficient_frontier_upper_hull(candidates)
    rust_time = time.time() - start_time
    
    print(f"   Hull points found: {len(hull_points)}")
    print(f"   Processing time: {rust_time:.4f}s")
    print(f"   Sample points: {hull_points[:3] if hull_points else 'None'}")
    
    # Test CVaR efficient frontier
    print("\n2. Testing CVaR Efficient Frontier:")
    start_time = time.time()
    cvar_hull_points = efficient_frontier_upper_hull_cvar(candidates)
    cvar_rust_time = time.time() - start_time
    
    print(f"   CVaR hull points found: {len(cvar_hull_points)}")
    print(f"   Processing time: {cvar_rust_time:.4f}s")
    print(f"   Sample points: {cvar_hull_points[:3] if cvar_hull_points else 'None'}")
    
    # Test with larger dataset to show scaling
    print("\n3. Testing Scaling Performance:")
    large_candidates = generate_test_candidates(5000)
    print(f"   Testing with {len(large_candidates)} candidates")
    
    start_time = time.time()
    large_hull = efficient_frontier_upper_hull(large_candidates)
    large_time = time.time() - start_time
    
    print(f"   Large dataset hull points: {len(large_hull)}")
    print(f"   Large dataset time: {large_time:.4f}s")
    
    # Performance summary
    print(f"\n📊 Performance Summary:")
    print(f"   2K candidates: {rust_time:.4f}s")
    print(f"   5K candidates: {large_time:.4f}s")
    if rust_time > 0:
        scaling_factor = large_time / rust_time
        expected_scaling = 5000 / 2000  # 2.5x
        efficiency = expected_scaling / scaling_factor
        print(f"   Scaling efficiency: {efficiency:.2f} (1.0 = linear scaling)")
    
    return {
        'success': True,
        'hull_points': len(hull_points),
        'cvar_hull_points': len(cvar_hull_points),
        'rust_time_2k': rust_time,
        'rust_time_5k': large_time
    }

if __name__ == "__main__":
    try:
        results = test_rust_integration()
        print(f"\n✅ Integration Test Results:")
        print(f"   Success: {results['success']}")
        print(f"   Standard Hull Points: {results['hull_points']}")
        print(f"   CVaR Hull Points: {results['cvar_hull_points']}")
        print(f"   Performance (2K): {results['rust_time_2k']:.4f}s")
        print(f"   Performance (5K): {results['rust_time_5k']:.4f}s")
        print(f"\n🎉 Rust efficient frontier integration is working correctly!")
        
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
