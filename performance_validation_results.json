{"timestamp": "2025-07-04T00:02:19.132403", "rust_available": true, "test_results": {}, "performance_metrics": {}, "accuracy_validation": {"standard_100": {"rust_points": 14, "python_points": 5, "jaccard_similarity": 0.26666666666666666, "identical_results": false}, "cvar_100": {"rust_points": 17, "python_points": 6, "jaccard_similarity": 0.15, "identical_results": false}, "standard_500": {"rust_points": 29, "python_points": 8, "jaccard_similarity": 0.23333333333333334, "identical_results": false}, "cvar_500": {"rust_points": 17, "python_points": 7, "jaccard_similarity": 0.3333333333333333, "identical_results": false}, "standard_1000": {"rust_points": 41, "python_points": 10, "jaccard_similarity": 0.18604651162790697, "identical_results": false}, "cvar_1000": {"rust_points": 20, "python_points": 5, "jaccard_similarity": 0.13636363636363635, "identical_results": false}, "standard_2000": {"rust_points": 51, "python_points": 8, "jaccard_similarity": 0.11320754716981132, "identical_results": false}, "cvar_2000": {"rust_points": 24, "python_points": 6, "jaccard_similarity": 0.15384615384615385, "identical_results": false}}, "scaling_analysis": {"conservative": {"500": {"rust_time": 0.0002256000880151987, "python_time": 0.0005846000276505947, "rust_cvar_time": 0.00013069994747638702, "python_cvar_time": 0.0008292999118566513, "speedup": 2.591311168332568, "cvar_speedup": 6.***************, "rust_hull_points": 33, "python_hull_points": 6, "candidates_per_second": 2216311.192069725}, "1000": {"rust_time": 0.00033199996687471867, "python_time": 0.0009417999535799026, "rust_cvar_time": 0.00015469989739358425, "python_cvar_time": 0.0010077999904751778, "speedup": 2.8367471311685226, "cvar_speedup": 6.51454853852394, "rust_hull_points": 44, "python_hull_points": 8, "candidates_per_second": 3012048.4932980533}, "2000": {"rust_time": 0.000646800035610795, "python_time": 0.0022426999639719725, "rust_cvar_time": 0.0003454000689089298, "python_cvar_time": 0.003162600100040436, "speedup": 3.***************, "cvar_speedup": 9.156338937715455, "rust_hull_points": 52, "python_hull_points": 9, "candidates_per_second": 3092145.7790448833}, "5000": {"rust_time": 0.0016918000765144825, "python_time": 0.005444600014016032, "rust_cvar_time": 0.0009838000405579805, "python_cvar_time": 0.006547700148075819, "speedup": 3.2182289678300675, "cvar_speedup": 6.655519290649926, "rust_hull_points": 74, "python_hull_points": 8, "candidates_per_second": 2955431.9505063565}, "10000": {"rust_time": 0.005071499850600958, "python_time": 0.04677150002680719, "rust_cvar_time": 0.001937400083988905, "python_cvar_time": 0.012575200060382485, "speedup": 9.222419679508597, "cvar_speedup": 6.490760563244871, "rust_hull_points": 88, "python_hull_points": 12, "candidates_per_second": 1971803.2721257063}, "20000": {"rust_time": 0.007311699911952019, "python_time": 0.026391500141471624, "rust_cvar_time": 0.005092500010505319, "python_cvar_time": 0.027218899922445416, "speedup": 3.609488964164263, "cvar_speedup": 5.344899335551408, "rust_hull_points": 111, "python_hull_points": 13, "candidates_per_second": 2735342.019070988}}, "balanced": {"500": {"rust_time": 0.00021069985814392567, "python_time": 0.0007234001532196999, "rust_cvar_time": 8.629984222352505e-05, "python_cvar_time": 0.0010565000120550394, "speedup": 3.4333205517659007, "cvar_speedup": 12.242200968555665, "rust_hull_points": 29, "python_hull_points": 8, "candidates_per_second": 2373043.8378295354}, "1000": {"rust_time": 0.00035630003549158573, "python_time": 0.0010391001123934984, "rust_cvar_time": 0.00019029993563890457, "python_cvar_time": 0.000964700011536479, "speedup": 2.9163626407161485, "cvar_speedup": 5.069365936975427, "rust_hull_points": 41, "python_hull_points": 10, "candidates_per_second": 2806623.3521989523}, "2000": {"rust_time": 0.0007487998809665442, "python_time": 0.0021914001554250717, "rust_cvar_time": 0.00041179987601935863, "python_cvar_time": 0.0024429999757558107, "speedup": 2.926549818085484, "cvar_speedup": 5.932493227950768, "rust_hull_points": 51, "python_hull_points": 8, "candidates_per_second": 2670940.595527897}, "5000": {"rust_time": 0.0017519998364150524, "python_time": 0.005564599996432662, "rust_cvar_time": 0.0011427998542785645, "python_cvar_time": 0.00679620006121695, "speedup": 3.176141847032911, "cvar_speedup": 5.946973160499139, "rust_hull_points": 63, "python_hull_points": 8, "candidates_per_second": 2853881.545006885}, "10000": {"rust_time": 0.004922299878671765, "python_time": 0.011558099882677197, "rust_cvar_time": 0.0019335998222231865, "python_cvar_time": 0.01214180001989007, "speedup": 2.348109657592832, "cvar_speedup": 6.2793758462026785, "rust_hull_points": 75, "python_hull_points": 11, "candidates_per_second": 2031570.6573120048}, "20000": {"rust_time": 0.006952600087970495, "python_time": 0.027118599973618984, "rust_cvar_time": 0.004710200009867549, "python_cvar_time": 0.03137199999764562, "speedup": 3.9004976024063343, "cvar_speedup": 6.660439032721203, "rust_hull_points": 100, "python_hull_points": 13, "candidates_per_second": 2876621.659083245}}, "aggressive": {"500": {"rust_time": 0.00031209993176162243, "python_time": 0.0010584001429378986, "rust_cvar_time": 0.00015430012717843056, "python_cvar_time": 0.00138669996522367, "speedup": 3.3912219620294244, "cvar_speedup": 8.987030604453807, "rust_hull_points": 27, "python_hull_points": 6, "candidates_per_second": 1602050.9750764477}, "1000": {"rust_time": 0.000350399874150753, "python_time": 0.0011170997750014067, "rust_cvar_time": 0.0001683998852968216, "python_cvar_time": 0.0009709000587463379, "speedup": 3.188071279160321, "cvar_speedup": 5.7654437058257475, "rust_hull_points": 38, "python_hull_points": 8, "candidates_per_second": 2853882.3035357846}, "2000": {"rust_time": 0.0008668999653309584, "python_time": 0.002213600091636181, "rust_cvar_time": 0.0004891999997198582, "python_cvar_time": 0.0026184001471847296, "speedup": 2.5534665822614144, "cvar_speedup": 5.352412405323312, "rust_hull_points": 44, "python_hull_points": 8, "candidates_per_second": 2307071.2654100237}, "5000": {"rust_time": 0.001825499814003706, "python_time": 0.007069000042974949, "rust_cvar_time": 0.0011049001477658749, "python_cvar_time": 0.0074847000651061535, "speedup": 3.872364154051126, "cvar_speedup": 6.774096356345261, "rust_hull_points": 57, "python_hull_points": 8, "candidates_per_second": 2738975.902185356}, "10000": {"rust_time": 0.0033148000948131084, "python_time": 0.012400300009176135, "rust_cvar_time": 0.002125700004398823, "python_cvar_time": 0.01243039988912642, "speedup": 3.740889240524556, "cvar_speedup": 5.847673643225074, "rust_hull_points": 69, "python_hull_points": 12, "candidates_per_second": 3016773.173033172}, "20000": {"rust_time": 0.007515599951148033, "python_time": 0.0261117999907583, "rust_cvar_time": 0.004885900067165494, "python_cvar_time": 0.027537399902939796, "speedup": 3.474346713567376, "cvar_speedup": 5.636095606620818, "rust_hull_points": 94, "python_hull_points": 13, "candidates_per_second": 2661131.530416934}}}, "rust_status": {"rust_available": true, "functions_available": ["convex_hull_upper_rust", "cross_product_rust", "efficient_frontier_upper_hull_rust", "efficient_frontier_upper_hull_cvar_rust"], "performance_improvement": "Expected 10-20x speedup for convex hull operations"}, "memory_efficiency": {"1000": {"baseline_mb": 118.55859375, "after_generation_mb": 118.55859375, "after_rust_mb": 118.5625, "after_python_mb": 118.5625, "generation_overhead_mb": 0.0, "rust_overhead_mb": 0.00390625, "python_overhead_mb": 0.0}, "5000": {"baseline_mb": 118.5625, "after_generation_mb": 118.57421875, "after_rust_mb": 118.57421875, "after_python_mb": 118.57421875, "generation_overhead_mb": 0.01171875, "rust_overhead_mb": 0.0, "python_overhead_mb": 0.0}, "10000": {"baseline_mb": 118.57421875, "after_generation_mb": 118.57421875, "after_rust_mb": 118.57421875, "after_python_mb": 118.57421875, "generation_overhead_mb": 0.0, "rust_overhead_mb": 0.0, "python_overhead_mb": 0.0}, "20000": {"baseline_mb": 118.57421875, "after_generation_mb": 124.515625, "after_rust_mb": 124.5703125, "after_python_mb": 124.5703125, "generation_overhead_mb": 5.94140625, "rust_overhead_mb": 0.0546875, "python_overhead_mb": 0.0}}, "real_world_scenarios": {"user_increased_params": {"config": {"description": "User's increased computational parameters", "max_combos": 100000, "candidates_per_category": 2000, "composite_portfolios": 200, "test_size": 12000}, "total_rust_time": 0.00354300020262599, "total_python_time": 0.015488500008359551, "speedup": 4.371577511308025, "groups_processed": 6, "total_hull_points": 266, "avg_time_per_group_rust": 0.0005905000337709984, "avg_time_per_group_python": 0.002581416668059925, "throughput_candidates_per_sec": 3386960.0095156296}, "original_params": {"config": {"description": "Original MPT parameters", "max_combos": 20000, "candidates_per_category": 200, "composite_portfolios": 20, "test_size": 1200}, "total_rust_time": 0.0003337003290653229, "total_python_time": 0.0010816000867635012, "speedup": 3.2412317056833784, "groups_processed": 6, "total_hull_points": 129, "avg_time_per_group_rust": 5.5616721510887146e-05, "avg_time_per_group_python": 0.0001802666811272502, "throughput_candidates_per_sec": 3596040.805117385}, "extreme_load": {"config": {"description": "Extreme computational load test", "max_combos": 200000, "candidates_per_category": 5000, "composite_portfolios": 500, "test_size": 30000}, "total_rust_time": 0.011194099904969335, "total_python_time": 0.037176299607381225, "speedup": 3.3210619811314848, "groups_processed": 6, "total_hull_points": 342, "avg_time_per_group_rust": 0.001865683317494889, "avg_time_per_group_python": 0.006196049934563537, "throughput_candidates_per_sec": 2679983.228189903}}}