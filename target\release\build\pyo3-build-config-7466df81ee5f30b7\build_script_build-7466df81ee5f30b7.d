C:\Users\<USER>\Desktop\MPT\target\release\build\pyo3-build-config-7466df81ee5f30b7\build_script_build-7466df81ee5f30b7.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.20.3\build.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.20.3\src\impl_.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.20.3\src\errors.rs

C:\Users\<USER>\Desktop\MPT\target\release\build\pyo3-build-config-7466df81ee5f30b7\build_script_build-7466df81ee5f30b7.exe: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.20.3\build.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.20.3\src\impl_.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.20.3\src\errors.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.20.3\build.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.20.3\src\impl_.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.20.3\src\errors.rs:

# env-dep:CARGO_PKG_VERSION=0.20.3
