{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\", \"resolve-config\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py313\", \"abi3-py314\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"python3-dll-a\", \"resolve-config\"]", "target": 8254743344416261242, "profile": 1369601567987815722, "path": 8548408913929344879, "deps": [[3346669234123344896, "target_lexicon", false, 10488921346172399598], [3722963349756955755, "once_cell", false, 13777515960127027739], [10288871127199797760, "build_script_build", false, 10596484436854589778]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\pyo3-build-config-e1932e6621f584f1\\dep-lib-pyo3_build_config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}