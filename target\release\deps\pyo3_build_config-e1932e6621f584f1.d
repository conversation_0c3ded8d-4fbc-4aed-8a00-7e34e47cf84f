C:\Users\<USER>\Desktop\MPT\target\release\deps\pyo3_build_config-e1932e6621f584f1.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\errors.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\impl_.rs C:\Users\<USER>\Desktop\MPT\target\release\build\pyo3-build-config-a646a7764dd09b7b\out/pyo3-build-config-file.txt C:\Users\<USER>\Desktop\MPT\target\release\build\pyo3-build-config-a646a7764dd09b7b\out/pyo3-build-config.txt

C:\Users\<USER>\Desktop\MPT\target\release\deps\libpyo3_build_config-e1932e6621f584f1.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\errors.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\impl_.rs C:\Users\<USER>\Desktop\MPT\target\release\build\pyo3-build-config-a646a7764dd09b7b\out/pyo3-build-config-file.txt C:\Users\<USER>\Desktop\MPT\target\release\build\pyo3-build-config-a646a7764dd09b7b\out/pyo3-build-config.txt

C:\Users\<USER>\Desktop\MPT\target\release\deps\libpyo3_build_config-e1932e6621f584f1.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\errors.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\impl_.rs C:\Users\<USER>\Desktop\MPT\target\release\build\pyo3-build-config-a646a7764dd09b7b\out/pyo3-build-config-file.txt C:\Users\<USER>\Desktop\MPT\target\release\build\pyo3-build-config-a646a7764dd09b7b\out/pyo3-build-config.txt

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\errors.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\impl_.rs:
C:\Users\<USER>\Desktop\MPT\target\release\build\pyo3-build-config-a646a7764dd09b7b\out/pyo3-build-config-file.txt:
C:\Users\<USER>\Desktop\MPT\target\release\build\pyo3-build-config-a646a7764dd09b7b\out/pyo3-build-config.txt:

# env-dep:CARGO_PKG_VERSION=0.25.1
# env-dep:OUT_DIR=C:\\Users\\<USER>\\Desktop\\MPT\\target\\release\\build\\pyo3-build-config-a646a7764dd09b7b\\out
