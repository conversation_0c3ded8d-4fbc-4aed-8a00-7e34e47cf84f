{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py313\", \"abi3-py314\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"arc_lock\", \"auto-initialize\", \"bigdecimal\", \"chrono\", \"chrono-local\", \"chrono-tz\", \"default\", \"either\", \"experimental-async\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"jiff-02\", \"lock_api\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"num-rational\", \"ordered-float\", \"parking_lot\", \"py-clone\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"time\", \"unindent\", \"uuid\"]", "target": 1859062398649441551, "profile": 3090641468231581632, "path": 8681212485604955630, "deps": [[629381703529241162, "indoc", false, 16401018344781987546], [3722963349756955755, "once_cell", false, 10802249507119394238], [4684437522915235464, "libc", false, 10730761157540435168], [5099523288940447918, "pyo3_ffi", false, 14340088717336797895], [5197680718850464868, "pyo3_macros", false, 8291266675149537377], [9768805234657844767, "build_script_build", false, 5268550966769542090], [14643204177830147187, "memoffset", false, 8052816519068297393], [14748792705540276325, "unindent", false, 13845445609147575258]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\pyo3-3198794e2c8467b6\\dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}