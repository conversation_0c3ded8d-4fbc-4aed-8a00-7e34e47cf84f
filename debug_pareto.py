#!/usr/bin/env python3
"""
Debug Pareto principle implementation
"""

def debug_pareto_frontier(candidates):
    """Debug version with detailed logging"""
    points = [(c['risk'], c['return']) for c in candidates]
    if len(points) < 2:
        return points

    # Remove duplicates and sort by risk ascending
    points = sorted(list(set(points)), key=lambda x: x[0])
    print(f"Sorted points: {points}")

    # Build Pareto frontier using proper Pareto dominance
    efficient = []
    
    for i, (risk, return_val) in enumerate(points):
        print(f"\nProcessing point {i+1}: ({risk:.2f}, {return_val:.2f})")
        is_dominated = False
        
        # Check if this point is dominated by any point already in the efficient set
        for j, (eff_risk, eff_return) in enumerate(efficient):
            # A point is dominated if another point has:
            # (lower or equal risk AND higher return) OR (lower risk AND equal or higher return)
            condition1 = eff_risk <= risk and eff_return > return_val
            condition2 = eff_risk < risk and eff_return >= return_val
            
            print(f"  Checking against efficient point {j+1}: ({eff_risk:.2f}, {eff_return:.2f})")
            print(f"    Condition 1 (eff_risk <= risk and eff_return > return_val): {condition1}")
            print(f"    Condition 2 (eff_risk < risk and eff_return >= return_val): {condition2}")
            
            if condition1 or condition2:
                is_dominated = True
                print(f"    -> Point is DOMINATED by ({eff_risk:.2f}, {eff_return:.2f})")
                break
        
        if not is_dominated:
            print(f"  Point is NOT dominated")
            
            # Remove any points in efficient set that are dominated by this new point
            original_efficient = efficient.copy()
            efficient = []
            for eff_risk, eff_return in original_efficient:
                # Keep point if it's not dominated by the new point
                dominated_by_new = (risk <= eff_risk and return_val > eff_return) or (risk < eff_risk and return_val >= eff_return)
                if not dominated_by_new:
                    efficient.append((eff_risk, eff_return))
                else:
                    print(f"    Removing dominated point: ({eff_risk:.2f}, {eff_return:.2f})")
            
            efficient.append((risk, return_val))
            print(f"  Added to efficient set. Current efficient: {efficient}")
        else:
            print(f"  Point rejected (dominated)")
    
    # Sort the final efficient frontier by risk
    efficient.sort(key=lambda x: x[0])
    
    return efficient

# Test data
candidates = [
    {'risk': 0.1, 'return': 0.05},
    {'risk': 0.15, 'return': 0.08},
    {'risk': 0.12, 'return': 0.06},  # Should be filtered out (dominated by 0.15, 0.08)
    {'risk': 0.2, 'return': 0.12},
    {'risk': 0.18, 'return': 0.10},  # Should be filtered out (dominated by 0.2, 0.12)
    {'risk': 0.25, 'return': 0.15}
]

print('Test candidates:')
for i, c in enumerate(candidates):
    print(f'  {i+1}. Risk: {c["risk"]:.2f}, Return: {c["return"]:.2f}')

print('\n' + '='*50)
result = debug_pareto_frontier(candidates)
print(f'\nFinal result: {result}')

print('\nManual verification:')
print('Point (0.12, 0.06) should be dominated by (0.15, 0.08) because:')
print('  0.15 > 0.12 (higher risk) but 0.08 > 0.06 (higher return)')
print('  Actually, (0.12, 0.06) should NOT be dominated by (0.15, 0.08)')
print('  Let me check (0.10, 0.05) vs (0.12, 0.06):')
print('  0.10 < 0.12 (lower risk) and 0.05 < 0.06 (lower return) - no dominance')
print('  So (0.12, 0.06) should be kept!')

print('\nPoint (0.18, 0.10) should be dominated by (0.20, 0.12) because:')
print('  0.20 > 0.18 (higher risk) but 0.12 > 0.10 (higher return)')
print('  Actually, (0.18, 0.10) should NOT be dominated by (0.20, 0.12)')
print('  But it should be dominated by (0.15, 0.08):')
print('  0.15 < 0.18 (lower risk) and 0.08 < 0.10 (lower return) - no dominance')

print('\nWait, let me reconsider the dominance definition...')
