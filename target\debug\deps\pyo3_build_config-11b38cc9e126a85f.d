c:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\deps\pyo3_build_config-11b38cc9e126a85f.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\errors.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\impl_.rs c:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\build\pyo3-build-config-31081349520b9fc4\out/pyo3-build-config-file.txt c:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\build\pyo3-build-config-31081349520b9fc4\out/pyo3-build-config.txt

c:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\deps\libpyo3_build_config-11b38cc9e126a85f.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\errors.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\impl_.rs c:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\build\pyo3-build-config-31081349520b9fc4\out/pyo3-build-config-file.txt c:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\build\pyo3-build-config-31081349520b9fc4\out/pyo3-build-config.txt

c:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\deps\libpyo3_build_config-11b38cc9e126a85f.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\errors.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\impl_.rs c:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\build\pyo3-build-config-31081349520b9fc4\out/pyo3-build-config-file.txt c:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\build\pyo3-build-config-31081349520b9fc4\out/pyo3-build-config.txt

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\errors.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\pyo3-build-config-0.25.1\src\impl_.rs:
c:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\build\pyo3-build-config-31081349520b9fc4\out/pyo3-build-config-file.txt:
c:\Users\<USER>\Desktop\MPT-Tickmill\target\debug\build\pyo3-build-config-31081349520b9fc4\out/pyo3-build-config.txt:

# env-dep:CARGO_PKG_VERSION=0.25.1
# env-dep:OUT_DIR=c:\\Users\\<USER>\\Desktop\\MPT-Tickmill\\target\\debug\\build\\pyo3-build-config-31081349520b9fc4\\out
