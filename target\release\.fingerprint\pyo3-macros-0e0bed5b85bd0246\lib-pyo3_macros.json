{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"experimental-async\", \"experimental-inspect\", \"multiple-pymethods\"]", "target": 13917622123232857288, "profile": 17251378959361963653, "path": 6293466453592724, "deps": [[3060637413840920116, "proc_macro2", false, 7827357382494318173], [4342566878770968593, "pyo3_macros_backend", false, 12069384320669951385], [4974441333307933176, "syn", false, 4073719181460523455], [17990358020177143287, "quote", false, 17534572221746784431]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\pyo3-macros-0e0bed5b85bd0246\\dep-lib-pyo3_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}