"""
Rust-optimized efficient frontier calculations wrapper
Provides high-performance implementations of convex hull and efficient frontier algorithms
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Any

# Try to import the Rust module, fall back to original if not available
try:
    import ratio_calcs_rust
    RUST_AVAILABLE = True
except ImportError:
    RUST_AVAILABLE = False

def efficient_frontier_upper_hull_rust(candidates: List[Dict[str, Any]]) -> List[Tuple[float, float]]:
    """
    Rust-optimized convex hull calculation for efficient frontier
    
    Args:
        candidates: List of portfolio candidates with 'risk' and 'return' keys
        
    Returns:
        List of (risk, return) tuples representing the upper hull
    """
    if not RUST_AVAILABLE:
        # Fallback to original Python implementation
        return efficient_frontier_upper_hull_python(candidates)
    
    # Extract points for Rust processing
    points = [(c['risk'], c['return']) for c in candidates]
    
    if len(points) < 2:
        return points
    
    try:
        # Call Rust implementation
        hull_points = ratio_calcs_rust.convex_hull_upper_rust(points)
        return hull_points
    except Exception as e:
        print(f"Rust convex hull failed, falling back to Python: {e}")
        return efficient_frontier_upper_hull_python(candidates)

def efficient_frontier_upper_hull_python(candidates: List[Dict[str, Any]]) -> List[Tuple[float, float]]:
    """
    Python fallback implementation using Pareto frontier for efficient frontier
    FIXED VERSION - USES PROPER PARETO OPTIMALITY
    """
    points = [(c['risk'], c['return']) for c in candidates]
    if len(points) < 2:
        return points

    # Remove duplicates and sort by risk ascending
    points = sorted(list(set(points)), key=lambda x: x[0])

    # Build Pareto frontier using proper Pareto dominance
    efficient = []

    for risk, return_val in points:
        is_dominated = False

        # Check if this point is dominated by any point already in the efficient set
        for eff_risk, eff_return in efficient:
            # A point is dominated if another point has:
            # (lower or equal risk AND higher return) OR (lower risk AND equal or higher return)
            if (eff_risk <= risk and eff_return > return_val) or (eff_risk < risk and eff_return >= return_val):
                is_dominated = True
                break

        if not is_dominated:
            # Remove any points in efficient set that are dominated by this new point
            efficient = [(eff_risk, eff_return) for eff_risk, eff_return in efficient
                        if not ((risk <= eff_risk and return_val > eff_return) or
                               (risk < eff_risk and return_val >= eff_return))]

            efficient.append((risk, return_val))

    # Sort the final efficient frontier by risk
    efficient.sort(key=lambda x: x[0])

    return efficient

def efficient_frontier_upper_hull_cvar_rust(candidates: List[Dict[str, Any]]) -> List[Tuple[float, float]]:
    """
    Rust-optimized convex hull calculation for CVaR-based efficient frontier
    
    Args:
        candidates: List of portfolio candidates with 'cvar_95' and 'return' keys
        
    Returns:
        List of (cvar, return) tuples representing the upper hull
    """
    if not RUST_AVAILABLE:
        # Fallback to original Python implementation
        return efficient_frontier_upper_hull_cvar_python(candidates)
    
    # Extract points for Rust processing (using CVaR instead of risk)
    points = [(c.get('cvar_95', 0), c['return']) for c in candidates]
    
    if len(points) < 2:
        return points
    
    try:
        # Call Rust implementation
        hull_points = ratio_calcs_rust.convex_hull_upper_rust(points)
        return hull_points
    except Exception as e:
        print(f"Rust CVaR convex hull failed, falling back to Python: {e}")
        return efficient_frontier_upper_hull_cvar_python(candidates)

def efficient_frontier_upper_hull_cvar_python(candidates: List[Dict[str, Any]]) -> List[Tuple[float, float]]:
    """
    Python fallback implementation using Pareto frontier for CVaR-based efficient frontier
    FIXED VERSION - USES PROPER PARETO OPTIMALITY
    """
    points = [(c.get('cvar_95', 0), c['return']) for c in candidates]
    if len(points) < 2:
        return points

    # Remove duplicates and sort by CVaR ascending
    points = sorted(list(set(points)), key=lambda x: x[0])

    # Build Pareto frontier using proper Pareto dominance
    efficient = []

    for cvar, return_val in points:
        is_dominated = False

        # Check if this point is dominated by any point already in the efficient set
        for eff_cvar, eff_return in efficient:
            # A point is dominated if another point has:
            # (lower or equal CVaR AND higher return) OR (lower CVaR AND equal or higher return)
            if (eff_cvar <= cvar and eff_return > return_val) or (eff_cvar < cvar and eff_return >= return_val):
                is_dominated = True
                break

        if not is_dominated:
            # Remove any points in efficient set that are dominated by this new point
            efficient = [(eff_cvar, eff_return) for eff_cvar, eff_return in efficient
                        if not ((cvar <= eff_cvar and return_val > eff_return) or
                               (cvar < eff_cvar and return_val >= eff_return))]

            efficient.append((cvar, return_val))

    # Sort the final efficient frontier by CVaR
    efficient.sort(key=lambda x: x[0])

    return efficient

def cross_product_rust(o: Tuple[float, float], a: Tuple[float, float], b: Tuple[float, float]) -> float:
    """
    Rust-optimized cross product calculation
    
    Args:
        o, a, b: Points as (x, y) tuples
        
    Returns:
        Cross product value
    """
    if RUST_AVAILABLE:
        try:
            return ratio_calcs_rust.cross_product_rust(o, a, b)
        except Exception:
            pass
    
    # Fallback to Python implementation
    return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0])

def batch_convex_hull_rust(groups: List[List[Dict[str, Any]]]) -> List[List[Tuple[float, float]]]:
    """
    Process multiple convex hulls in batch for better performance
    
    Args:
        groups: List of candidate groups to process
        
    Returns:
        List of hull results for each group
    """
    results = []
    for group in groups:
        hull_points = efficient_frontier_upper_hull_rust(group)
        results.append(hull_points)
    
    return results

def get_rust_status() -> Dict[str, Any]:
    """
    Get status information about Rust backend availability and performance
    
    Returns:
        Dictionary with status information
    """
    return {
        'rust_available': RUST_AVAILABLE,
        'functions_available': [
            'convex_hull_upper_rust',
            'cross_product_rust',
            'efficient_frontier_upper_hull_rust',
            'efficient_frontier_upper_hull_cvar_rust'
        ] if RUST_AVAILABLE else [],
        'performance_improvement': 'Expected 10-20x speedup for convex hull operations' if RUST_AVAILABLE else 'N/A'
    }

# Compatibility aliases for existing code
def efficient_frontier_upper_hull(candidates):
    """Compatibility wrapper for existing code"""
    return efficient_frontier_upper_hull_rust(candidates)

def efficient_frontier_upper_hull_cvar(candidates):
    """Compatibility wrapper for existing CVaR code"""
    return efficient_frontier_upper_hull_cvar_rust(candidates)
