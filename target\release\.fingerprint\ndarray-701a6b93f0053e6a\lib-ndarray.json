{"rustc": 1842507548689473721, "features": "[\"default\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 2040997289075261528, "path": 2525462169438355074, "deps": [[5157631553186200874, "num_traits", false, 1799912323224782872], [12319020793864570031, "num_complex", false, 14967994221055254516], [15709748443193639506, "rawpointer", false, 18169895621523116006], [15826188163127377936, "matrixmultiply", false, 508481086961209645], [16795989132585092538, "num_integer", false, 4847168589489861484]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\ndarray-701a6b93f0053e6a\\dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}