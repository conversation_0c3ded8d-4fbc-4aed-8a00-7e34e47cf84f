#!/usr/bin/env python3
"""
Test script for Rust-optimized efficient frontier functions
"""

import numpy as np
import time
from efficient_frontier_rust_wrapper import (
    efficient_frontier_upper_hull_rust,
    efficient_frontier_upper_hull_python,
    cross_product_rust,
    get_rust_status
)

def test_rust_efficient_frontier():
    print("Testing Rust Efficient Frontier Implementation")
    print("=" * 50)
    
    # Check Rust availability
    status = get_rust_status()
    print(f"Rust Available: {status['rust_available']}")
    print(f"Functions: {status['functions_available']}")
    print()
    
    # Generate test data similar to MPT portfolios
    np.random.seed(42)
    n_portfolios = 2000  # Simulate the increased load
    
    candidates = []
    for i in range(n_portfolios):
        risk = np.random.uniform(0.01, 0.05)  # 1% to 5% risk
        return_val = np.random.uniform(0.001, 0.003)  # 0.1% to 0.3% return
        candidates.append({
            'risk': risk,
            'return': return_val,
            'combo': f'Portfolio_{i}',
            'weights': [0.33, 0.33, 0.34]
        })
    
    print(f"Testing with {len(candidates)} portfolio candidates")
    
    # Test cross product function
    print("\n1. Testing Cross Product Function:")
    o = (0.0, 0.0)
    a = (1.0, 0.0)
    b = (0.0, 1.0)
    
    cross_result = cross_product_rust(o, a, b)
    expected = 1.0  # Should be 1.0 for this configuration
    print(f"   Cross product result: {cross_result}")
    print(f"   Expected: {expected}")
    print(f"   ✓ Cross product test: {'PASSED' if abs(cross_result - expected) < 1e-10 else 'FAILED'}")
    
    # Test convex hull with performance comparison
    print("\n2. Testing Convex Hull Performance:")
    
    # Warm up
    for _ in range(5):
        efficient_frontier_upper_hull_rust(candidates[:100])
        efficient_frontier_upper_hull_python(candidates[:100])
    
    # Test Rust implementation
    start_time = time.time()
    rust_hull = efficient_frontier_upper_hull_rust(candidates)
    rust_time = time.time() - start_time
    
    # Test Python implementation
    start_time = time.time()
    python_hull = efficient_frontier_upper_hull_python(candidates)
    python_time = time.time() - start_time
    
    print(f"   Rust hull points: {len(rust_hull)}")
    print(f"   Python hull points: {len(python_hull)}")
    print(f"   Rust time: {rust_time:.4f}s")
    print(f"   Python time: {python_time:.4f}s")
    
    if rust_time > 0:
        speedup = python_time / rust_time
        print(f"   Speedup: {speedup:.2f}x")
    
    # Verify results are similar (allowing for small numerical differences)
    if len(rust_hull) == len(python_hull):
        max_diff = 0.0
        for (r_risk, r_ret), (p_risk, p_ret) in zip(rust_hull, python_hull):
            diff = max(abs(r_risk - p_risk), abs(r_ret - p_ret))
            max_diff = max(max_diff, diff)
        
        print(f"   Max difference: {max_diff:.2e}")
        print(f"   ✓ Results match: {'PASSED' if max_diff < 1e-10 else 'FAILED'}")
    else:
        print(f"   ⚠️  Different number of hull points - this may be due to numerical precision")
    
    # Test with larger dataset to show scaling benefits
    print("\n3. Testing Scaling Performance:")
    large_candidates = candidates * 5  # 10,000 candidates
    print(f"   Testing with {len(large_candidates)} candidates")
    
    # Rust implementation
    start_time = time.time()
    large_rust_hull = efficient_frontier_upper_hull_rust(large_candidates)
    large_rust_time = time.time() - start_time
    
    # Python implementation
    start_time = time.time()
    large_python_hull = efficient_frontier_upper_hull_python(large_candidates)
    large_python_time = time.time() - start_time
    
    print(f"   Large dataset - Rust time: {large_rust_time:.4f}s")
    print(f"   Large dataset - Python time: {large_python_time:.4f}s")
    
    if large_rust_time > 0:
        large_speedup = large_python_time / large_rust_time
        print(f"   Large dataset speedup: {large_speedup:.2f}x")
    
    print("\n🎉 Efficient Frontier Rust testing completed!")
    
    return {
        'rust_available': status['rust_available'],
        'small_speedup': python_time / rust_time if rust_time > 0 else 0,
        'large_speedup': large_python_time / large_rust_time if large_rust_time > 0 else 0,
        'rust_hull_points': len(rust_hull),
        'python_hull_points': len(python_hull)
    }

if __name__ == "__main__":
    results = test_rust_efficient_frontier()
    print(f"\n✅ Test Results Summary:")
    print(f"   Rust Available: {results['rust_available']}")
    print(f"   Small Dataset Speedup: {results['small_speedup']:.2f}x")
    print(f"   Large Dataset Speedup: {results['large_speedup']:.2f}x")
    print(f"   Hull Points (Rust/Python): {results['rust_hull_points']}/{results['python_hull_points']}")
