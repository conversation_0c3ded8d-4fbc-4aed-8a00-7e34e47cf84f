{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py313\", \"abi3-py314\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"generate-import-lib\"]", "target": 14506753996192664611, "profile": 3090641468231581632, "path": 2589292419932977380, "deps": [[4684437522915235464, "libc", false, 10730761157540435168], [5099523288940447918, "build_script_build", false, 8813567623739955986]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\pyo3-ffi-a723fdc9e173aa5d\\dep-lib-pyo3_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}