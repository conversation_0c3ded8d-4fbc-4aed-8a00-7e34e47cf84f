# Signal Handling and Graceful Shutdown Fix

## Problem Description

The MPT optimization occasionally hangs and produces errors when interrupted with Ctrl+C:

```
BufferError: Existing exports of data: object cannot be re-sized
OSError: [WinError 10038] An operation was attempted on something that is not a socket
```

These errors occur because:
1. **Missing Signal Handling**: No graceful shutdown mechanism for Ctrl+C interrupts
2. **Pandas DataFrame Export Issues**: Multiple processes accessing shared DataFrames simultaneously
3. **Multiprocessing Cleanup Issues**: Loky backend processes not properly terminated
4. **Resource Contention**: Shared data structures causing conflicts during shutdown

## Solution Implemented

### 1. Signal Handling (`matrix23.py`)

Added comprehensive signal handling with graceful shutdown:

```python
# Global variables for graceful shutdown
_shutdown_requested = False
_active_parallel_jobs = []
_shutdown_lock = threading.Lock()

def signal_handler(signum, frame):
    """Handle interrupt signals gracefully"""
    # Implements two-stage shutdown:
    # 1. First Ctrl+C: Graceful shutdown with cleanup
    # 2. Second Ctrl+C: Immediate forced termination
```

**Features:**
- **Two-stage shutdown**: First Ctrl+C triggers graceful cleanup, second forces immediate exit
- **Parallel job tracking**: Maintains list of active parallel jobs for proper termination
- **Memory cleanup**: Forces garbage collection and clears pandas caches
- **MT5 cleanup**: Ensures MetaTrader5 connection is properly closed

### 2. Safe Parallel Execution

Enhanced parallel processing with proper error handling:

```python
def safe_parallel_execute(parallel_job, delayed_tasks):
    """Execute parallel tasks with proper cleanup and error handling"""
    # Handles interruptions and ensures proper cleanup
```

**Features:**
- **Interrupt detection**: Checks for shutdown requests before and during execution
- **Proper cleanup**: Terminates parallel job pools with timeout
- **Error handling**: Distinguishes between interrupts and other errors
- **Resource tracking**: Removes jobs from active list after completion

### 3. DataFrame Protection (`func_rest.py`)

Improved DataFrame handling to prevent export conflicts:

```python
def cached_process_combo(combo_data, frontier_option=None):
    # Create deep copies to avoid export conflicts
    compact_current_df = current_df[list(combo)].copy(deep=True)
    compact_historical_df = historical_df[list(combo)].copy(deep=True)
    
    # Ensure DataFrames are not sharing memory with parent
    compact_current_df = compact_current_df.reset_index(drop=True)
    compact_historical_df = compact_historical_df.reset_index(drop=True)
```

**Features:**
- **Deep copying**: Prevents shared memory issues between processes
- **Memory isolation**: Resets indices to ensure independent DataFrames
- **Error handling**: Gracefully handles DataFrame processing errors

### 4. Shutdown Checks in Optimization Loop

Added shutdown request checks at critical points:

```python
# Check for shutdown request during chunk preparation
if _shutdown_requested:
    raise InterruptedError("Shutdown requested during chunk preparation")

# Check for shutdown request before starting parallel processing
if _shutdown_requested:
    raise InterruptedError("Shutdown requested before parallel processing")
```

**Features:**
- **Early detection**: Checks for shutdown requests before expensive operations
- **Clean interruption**: Raises appropriate exceptions for proper handling
- **Resource conservation**: Prevents unnecessary work when shutdown is requested

## Usage Instructions

### Normal Operation
The application works exactly as before. The signal handling is transparent during normal operation.

### Graceful Shutdown
1. **Press Ctrl+C once**: Initiates graceful shutdown
   - Terminates active parallel jobs
   - Cleans up memory and caches
   - Closes MT5 connection
   - Displays shutdown progress

2. **Press Ctrl+C twice quickly**: Forces immediate termination
   - Use only if graceful shutdown hangs

### Testing the Fix

Run the test script to verify signal handling:

```bash
python test_signal_handling.py
```

This script simulates the optimization process and allows you to test:
- Graceful shutdown with Ctrl+C
- Forced shutdown with double Ctrl+C
- Parallel job cleanup
- Memory management

## Technical Details

### Signal Registration
```python
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
if hasattr(signal, 'SIGTERM'):
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal
```

### Parallel Job Tracking
- All parallel jobs are tracked in `_active_parallel_jobs` list
- Jobs are added when created and removed when completed
- During shutdown, all active jobs are terminated with timeout

### Memory Management Integration
- Works with existing memory manager
- Adds additional cleanup during shutdown
- Forces garbage collection to prevent export errors

### Error Handling
- Distinguishes between `KeyboardInterrupt`, `InterruptedError`, and other exceptions
- Provides appropriate error messages for different shutdown scenarios
- Maintains application stability during interruption

## Benefits

1. **No More Hanging**: Ctrl+C now properly terminates the optimization
2. **Clean Shutdown**: Resources are properly cleaned up
3. **No Export Errors**: DataFrame conflicts are prevented
4. **Responsive Interface**: Application responds quickly to interruption
5. **Backward Compatible**: No changes to normal operation

## Files Modified

- `matrix23.py`: Added signal handling and shutdown checks
- `func_rest.py`: Enhanced DataFrame handling
- `test_signal_handling.py`: Test script for verification

The fix is comprehensive and addresses all the root causes of the hanging and error issues during Ctrl+C interruption.
