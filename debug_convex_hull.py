#!/usr/bin/env python3
"""
Debug convex hull algorithm step by step
"""

import ratio_calcs_rust

def debug_python_algorithm():
    """Debug the Python algorithm step by step"""
    print("🐍 Python Algorithm Debug")
    print("=" * 40)
    
    # Simple test case
    candidates = [
        {'risk': 0.01, 'return': 0.001, 'cvar_95': 0.012},
        {'risk': 0.02, 'return': 0.002, 'cvar_95': 0.024},
        {'risk': 0.03, 'return': 0.003, 'cvar_95': 0.036},
        {'risk': 0.015, 'return': 0.0025, 'cvar_95': 0.018},
        {'risk': 0.025, 'return': 0.0015, 'cvar_95': 0.030},
    ]
    
    points = [(c['risk'], c['return']) for c in candidates]
    print(f"Original points: {points}")
    
    # Sort by risk ascending, then return ascending
    points = sorted(points, key=lambda x: (x[0], x[1]))
    print(f"Sorted points: {points}")
    
    def cross(o, a, b):
        """Cross product of OA x OB > 0 => counter-clockwise turn."""
        return (a[0] - o[0])*(b[1] - o[1]) - (a[1] - o[1])*(b[0] - o[0])
    
    # Build upper hull
    upper = []
    print(f"\nBuilding upper hull (processing in reverse order):")
    for i, p in enumerate(reversed(points)):
        print(f"  Step {i+1}: Processing point {p}")
        print(f"    Current upper: {upper}")
        
        while len(upper) >= 2:
            cross_product = cross(upper[-2], upper[-1], p)
            print(f"    Cross product of {upper[-2]}, {upper[-1]}, {p} = {cross_product}")
            if cross_product <= 0:
                removed = upper.pop()
                print(f"    Removed {removed} (cross product <= 0)")
            else:
                print(f"    Keeping point (cross product > 0)")
                break
        
        upper.append(p)
        print(f"    Added {p}, upper now: {upper}")
    
    print(f"\nFinal upper hull: {upper}")
    
    # Return the upper hull points in correct order
    result = list(reversed(upper))[1:] if len(upper) > 1 else upper
    print(f"After reversing and removing first: {result}")
    
    return result

def debug_rust_algorithm():
    """Debug the Rust algorithm by calling it directly"""
    print("\n🦀 Rust Algorithm Debug")
    print("=" * 40)
    
    # Same test case
    points = [(0.01, 0.001), (0.02, 0.002), (0.03, 0.003), (0.015, 0.0025), (0.025, 0.0015)]
    print(f"Input points: {points}")
    
    result = ratio_calcs_rust.convex_hull_upper_rust(points)
    print(f"Rust result: {result}")
    
    return result

def test_cross_product():
    """Test cross product calculation"""
    print("\n➕ Cross Product Test")
    print("=" * 40)
    
    # Test points
    o = (0.01, 0.001)
    a = (0.015, 0.0025) 
    b = (0.02, 0.002)
    
    # Python calculation
    python_cross = (a[0] - o[0])*(b[1] - o[1]) - (a[1] - o[1])*(b[0] - o[0])
    print(f"Python cross product of {o}, {a}, {b} = {python_cross}")
    
    # Rust calculation
    rust_cross = ratio_calcs_rust.cross_product_rust(o, a, b)
    print(f"Rust cross product of {o}, {a}, {b} = {rust_cross}")
    
    print(f"Match: {'✓' if abs(python_cross - rust_cross) < 1e-10 else '✗'}")

def test_simple_hull():
    """Test with a very simple case"""
    print("\n🔺 Simple Hull Test")
    print("=" * 40)
    
    # Three points forming a triangle
    points = [(0.01, 0.001), (0.02, 0.003), (0.03, 0.002)]
    print(f"Triangle points: {points}")
    
    # Expected: the upper edge should be from (0.01, 0.001) to (0.02, 0.003)
    # because (0.02, 0.003) has the highest return
    
    rust_result = ratio_calcs_rust.convex_hull_upper_rust(points)
    print(f"Rust result: {rust_result}")
    
    # Manual Python calculation
    sorted_points = sorted(points, key=lambda x: (x[0], x[1]))
    print(f"Sorted: {sorted_points}")
    
    upper = []
    for p in reversed(sorted_points):
        while len(upper) >= 2:
            cross_val = (upper[-2][0] - upper[-1][0]) * (p[1] - upper[-1][1]) - (upper[-2][1] - upper[-1][1]) * (p[0] - upper[-1][0])
            if cross_val <= 0:
                upper.pop()
            else:
                break
        upper.append(p)
    
    python_result = list(reversed(upper))[1:] if len(upper) > 1 else upper
    print(f"Python result: {python_result}")
    
    print(f"Match: {'✓' if rust_result == python_result else '✗'}")

def main():
    print("🔍 Convex Hull Algorithm Debug")
    print("=" * 50)
    
    test_cross_product()
    test_simple_hull()
    python_result = debug_python_algorithm()
    rust_result = debug_rust_algorithm()
    
    print(f"\n📊 Final Comparison:")
    print(f"Python: {python_result}")
    print(f"Rust:   {rust_result}")
    print(f"Match:  {'✓' if python_result == rust_result else '✗'}")

if __name__ == "__main__":
    main()
