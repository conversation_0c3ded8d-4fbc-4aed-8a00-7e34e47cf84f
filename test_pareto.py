#!/usr/bin/env python3
"""
Test Pareto principle implementation in efficient frontier
"""

from efficient_frontier_rust_wrapper import efficient_frontier_upper_hull_rust, efficient_frontier_upper_hull_python

# Test data with some portfolios that should be filtered by Pareto principle
candidates = [
    {'risk': 0.1, 'return': 0.05},
    {'risk': 0.15, 'return': 0.08},
    {'risk': 0.12, 'return': 0.06},  # Should be filtered out (dominated)
    {'risk': 0.2, 'return': 0.12},
    {'risk': 0.18, 'return': 0.10},  # Should be filtered out (dominated)
    {'risk': 0.25, 'return': 0.15}
]

print('Test candidates:')
for i, c in enumerate(candidates):
    print(f'  {i+1}. Risk: {c["risk"]:.2f}, Return: {c["return"]:.2f}')

print('\nExpected efficient frontier (Pareto optimal):')
print('  Should include: (0.1, 0.05), (0.15, 0.08), (0.2, 0.12), (0.25, 0.15)')
print('  Should exclude: (0.12, 0.06) and (0.18, 0.10) as they are dominated')

rust_result = efficient_frontier_upper_hull_rust(candidates)
python_result = efficient_frontier_upper_hull_python(candidates)

print(f'\nRust result: {rust_result}')
print(f'Python result: {python_result}')
print(f'Results match: {rust_result == python_result}')

# Verify Pareto optimality
print('\nPareto optimality check:')
for point in rust_result:
    risk, ret = point
    dominated = False
    for other in rust_result:
        other_risk, other_ret = other
        if other_risk < risk and other_ret >= ret:
            dominated = True
            break
        if other_risk <= risk and other_ret > ret:
            dominated = True
            break
    print(f'  Point ({risk:.2f}, {ret:.2f}) - {"DOMINATED" if dominated else "PARETO OPTIMAL"}')
